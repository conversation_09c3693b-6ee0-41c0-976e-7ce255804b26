import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
// import { ElasticsearchModule } from '@nestjs/elasticsearch';
import { AssetsService } from './assets.service';
import { AssetsController } from './assets.controller';
import { Asset } from './entities/asset.entity';
import { Category } from '../categories/entities/category.entity';
import { Tag } from '../tags/entities/tag.entity';
import { LoggerService } from '../../common/services/logger.service';
import { CacheService } from '../../common/services/cache.service';
import { StorageService } from '../../common/services/storage.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Asset, Category, Tag]),
    // 暂时禁用 Elasticsearch 模块以解决启动问题
    // ...(process.env.ELASTICSEARCH_NODE ? [ElasticsearchModule] : []),
  ],
  controllers: [AssetsController],
  providers: [
    AssetsService,
    LoggerService,
    CacheService,
    StorageService,
  ],
  exports: [AssetsService],
})
export class AssetsModule {}
