# DL引擎 Kubernetes 部署指南

本文档提供了在Kubernetes集群上部署DL（Digital Learning）引擎的详细指南。DL引擎是一个基于微服务架构的3D引擎和编辑器平台，支持大规模并发用户访问。

## 目录

- [系统概述](#系统概述)
- [系统要求](#系统要求)
- [部署架构](#部署架构)
- [前置准备](#前置准备)
- [部署步骤](#部署步骤)
  - [创建命名空间](#创建命名空间)
  - [配置ConfigMap和Secret](#配置configmap和secret)
  - [部署数据库和Redis](#部署数据库和redis)
  - [部署MinIO对象存储](#部署minio对象存储)
  - [部署服务注册中心](#部署服务注册中心)
  - [部署微服务](#部署微服务)
  - [部署协作服务](#部署协作服务)
  - [部署API网关](#部署api网关)
  - [部署前端应用](#部署前端应用)
  - [部署游戏服务器](#部署游戏服务器)
- [服务暴露和访问](#服务暴露和访问)
- [监控和日志](#监控和日志)
- [扩展和高可用性](#扩展和高可用性)
- [安全配置](#安全配置)
- [备份和恢复](#备份和恢复)
- [故障排除](#故障排除)
- [维护和更新](#维护和更新)
- [生产环境建议](#生产环境建议)

## 系统概述

DL引擎采用现代微服务架构，包含以下核心组件：

### 基础设施层
- **MySQL 8.0**：主数据存储，支持多数据库分离
- **Redis 7.0**：缓存和会话存储，支持实时协作
- **MinIO**：对象存储服务，用于资产文件管理

### 核心微服务层
- **服务注册中心**：服务发现和注册（端口：3010/4010）
- **API网关**：统一入口，路由和认证（端口：3000）
- **用户服务**：用户管理和认证（端口：3001/4001）
- **项目服务**：项目和场景管理（端口：3002/4002）
- **资产服务**：资产文件管理（端口：3003/4003）
- **渲染服务**：3D渲染和图像处理（端口：3004/4004）
- **协作服务**：实时协作功能（端口：3005-3007）
- **游戏服务器**：游戏实例管理（端口：3030）

### 前端应用层
- **编辑器**：基于React的可视化编辑器（端口：80）

### 监控和运维层
- **监控服务**：系统监控和告警（端口：3100）
- **Prometheus**：指标收集（端口：9090）
- **Grafana**：监控仪表板（端口：3000）
- **ELK Stack**：日志分析（Elasticsearch:9200, Kibana:5601）

## 系统要求

### 硬件要求
- **CPU**：4核心以上（推荐8核心）
- **内存**：16GB以上（推荐32GB）
- **存储**：100GB以上可用空间（推荐SSD）
- **网络**：稳定的网络连接，支持负载均衡

### 软件要求
- **Kubernetes集群**：v1.20+（推荐v1.25+）
- **Helm**：v3.0+
- **kubectl**：命令行工具
- **容器仓库**：Docker Hub、阿里云容器服务、Harbor等
- **持久化存储**：支持ReadWriteOnce和ReadWriteMany的存储类
- **Ingress控制器**：Nginx Ingress、Traefik、Istio等
- **DNS**：集群内DNS解析正常工作

### 集群配置要求
- **节点数量**：至少3个节点（1个Master，2个Worker）
- **网络插件**：Calico、Flannel、Weave等
- **存储插件**：支持动态卷供应
- **监控组件**：Prometheus Operator（可选）
- **日志收集**：Fluentd、Filebeat等（可选）

## 部署架构

DL引擎在Kubernetes中的部署架构如下：

```
                                    ┌─────────────┐
                                    │   Ingress   │
                                    │ (Nginx/ALB) │
                                    └──────┬──────┘
                                           │
                                    ┌──────┴──────┐
                                    │ API Gateway │
                                    │  (端口:3000) │
                                    └──────┬──────┘
                                           │
                 ┌───────────┬─────────────┼─────────────┬───────────┐
                 │           │             │             │           │
        ┌────────┴────────┐  │  ┌──────────┴─────────┐   │  ┌────────┴────────┐
        │  User Service   │  │  │   Project Service  │   │  │  Asset Service  │
        │   (端口:3001)   │  │  │    (端口:3002)     │   │  │   (端口:3003)   │
        └────────┬────────┘  │  └──────────┬─────────┘   │  └────────┬────────┘
                 │           │             │             │           │
        ┌────────┴────────┐  │  ┌──────────┴─────────┐   │  ┌────────┴────────┐
        │  User Database  │  │  │  Project Database  │   │  │     MinIO       │
        │   (MySQL:3306)  │  │  │    (MySQL:3306)    │   │  │  (端口:9000)    │
        └─────────────────┘  │  └────────────────────┘   │  └─────────────────┘
                             │                           │
                     ┌───────┴────────┐          ┌───────┴────────┐
                     │Service Registry│          │ Render Service │
                     │   (端口:3010)   │          │   (端口:3004)   │
                     └────────┬───────┘          └────────┬───────┘
                              │                           │
                     ┌────────┴───────┐          ┌───────┴────────┐
                     │Registry Database│         │     Redis      │
                     │   (MySQL:3306)  │         │   (端口:6379)   │
                     └────────────────┘          └────────────────┘
                                                          │
                                                 ┌────────┴────────┐
                                                 │Collaboration Svc│
                                                 │   (端口:3005-7)  │
                                                 └─────────────────┘
```

### 网络架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        Kubernetes 集群                          │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │   前端层     │  │   网关层     │  │   服务层     │              │
│  │             │  │             │  │             │              │
│  │   Editor    │  │ API Gateway │  │ Microservices│              │
│  │   (Pod)     │  │   (Pod)     │  │   (Pods)    │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │   数据层     │  │   缓存层     │  │   存储层     │              │
│  │             │  │             │  │             │              │
│  │   MySQL     │  │    Redis    │  │    MinIO    │              │
│  │ (StatefulSet)│  │ (Deployment)│  │ (Deployment)│              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │   监控层     │  │   日志层     │  │   告警层     │              │
│  │             │  │             │  │             │              │
│  │ Prometheus  │  │     ELK     │  │Alertmanager │              │
│  │  Grafana    │  │   Stack     │  │             │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
└─────────────────────────────────────────────────────────────────┘
```

## 前置准备

### 1. 环境检查

```bash
# 检查Kubernetes集群状态
kubectl cluster-info
kubectl get nodes

# 检查存储类
kubectl get storageclass

# 检查Ingress控制器
kubectl get pods -n ingress-nginx
# 或者
kubectl get pods -n kube-system | grep ingress
```

### 2. 构建和推送Docker镜像

```bash
# 构建所有微服务镜像
cd newsystem

# 构建服务注册中心
docker build -t your-registry/dl-engine-service-registry:latest ./server/service-registry
docker push your-registry/dl-engine-service-registry:latest

# 构建用户服务
docker build -t your-registry/dl-engine-user-service:latest ./server/user-service
docker push your-registry/dl-engine-user-service:latest

# 构建项目服务
docker build -t your-registry/dl-engine-project-service:latest ./server/project-service
docker push your-registry/dl-engine-project-service:latest

# 构建资产服务
docker build -t your-registry/dl-engine-asset-service:latest ./server/asset-service
docker push your-registry/dl-engine-asset-service:latest

# 构建渲染服务
docker build -t your-registry/dl-engine-render-service:latest ./server/render-service
docker push your-registry/dl-engine-render-service:latest

# 构建协作服务
docker build -t your-registry/dl-engine-collaboration-service:latest ./server/collaboration-service
docker push your-registry/dl-engine-collaboration-service:latest

# 构建API网关
docker build -t your-registry/dl-engine-api-gateway:latest ./server/api-gateway
docker push your-registry/dl-engine-api-gateway:latest

# 构建游戏服务器
docker build -t your-registry/dl-engine-game-server:latest ./server/game-server
docker push your-registry/dl-engine-game-server:latest

# 构建编辑器
docker build -t your-registry/dl-engine-editor:latest ./editor
docker push your-registry/dl-engine-editor:latest

# 构建监控服务
docker build -t your-registry/dl-engine-monitoring-service:latest ./server/monitoring-service
docker push your-registry/dl-engine-monitoring-service:latest
```

### 3. 安装必要工具

```bash
# 安装Helm（如果未安装）
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash

# 验证Helm安装
helm version

# 添加常用Helm仓库
helm repo add stable https://charts.helm.sh/stable
helm repo add bitnami https://charts.bitnami.com/bitnami
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo add elastic https://helm.elastic.co
helm repo update
```

### 4. 准备配置文件

创建部署配置目录：

```bash
mkdir -p k8s-deployment/{base,overlays/{dev,staging,prod}}
cd k8s-deployment
```

## 部署步骤

### 创建命名空间

首先，为DL引擎创建一个专用的命名空间：

```bash
# 创建主命名空间
kubectl create namespace dl-engine

# 创建监控命名空间
kubectl create namespace dl-engine-monitoring

# 验证命名空间创建
kubectl get namespaces | grep dl-engine
```

或者使用YAML文件创建：

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: dl-engine
  labels:
    name: dl-engine
    app: dl-engine
---
apiVersion: v1
kind: Namespace
metadata:
  name: dl-engine-monitoring
  labels:
    name: dl-engine-monitoring
    app: dl-engine-monitoring
```

```bash
kubectl apply -f namespace.yaml
```

### 部署MinIO对象存储

DL引擎使用MinIO作为对象存储服务，用于存储3D模型、纹理、音频和渲染输出等资源文件。

```yaml
# minio-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: minio
  namespace: dl-engine
  labels:
    app: minio
    component: storage
spec:
  selector:
    matchLabels:
      app: minio
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: minio
        component: storage
    spec:
      containers:
      - name: minio
        image: minio/minio:RELEASE.2024-01-16T16-07-38Z
        args:
        - server
        - /data
        - --console-address
        - ":9001"
        env:
        - name: MINIO_ROOT_USER
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MINIO_ROOT_USER
        - name: MINIO_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MINIO_ROOT_PASSWORD
        - name: MINIO_BROWSER_REDIRECT_URL
          value: "http://localhost:9001"
        ports:
        - containerPort: 9000
          name: api
        - containerPort: 9001
          name: console
        volumeMounts:
        - name: minio-data
          mountPath: /data
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        readinessProbe:
          httpGet:
            path: /minio/health/ready
            port: 9000
          initialDelaySeconds: 20
          periodSeconds: 10
          timeoutSeconds: 5
        livenessProbe:
          httpGet:
            path: /minio/health/live
            port: 9000
          initialDelaySeconds: 30
          periodSeconds: 20
          timeoutSeconds: 5
        securityContext:
          runAsUser: 1000
          runAsGroup: 1000
          fsGroup: 1000
      volumes:
      - name: minio-data
        persistentVolumeClaim:
          claimName: minio-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: minio
  namespace: dl-engine
  labels:
    app: minio
    component: storage
spec:
  ports:
  - port: 9000
    targetPort: 9000
    name: api
  - port: 9001
    targetPort: 9001
    name: console
  selector:
    app: minio
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: minio-pvc
  namespace: dl-engine
  labels:
    app: minio
    component: storage
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: "fast-ssd"  # 根据您的存储类调整
  resources:
    requests:
      storage: 100Gi
```

创建初始化MinIO的Job，用于创建必要的存储桶：

```yaml
# minio-init-job.yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: minio-init
  namespace: dl-engine
  labels:
    app: minio
    component: init
spec:
  template:
    metadata:
      labels:
        app: minio-init
        component: init
    spec:
      containers:
      - name: mc
        image: minio/mc:RELEASE.2024-01-13T07-53-27Z
        command: ["/bin/sh", "-c"]
        args:
        - |
          echo "等待MinIO服务启动..."
          until mc alias set myminio http://minio:9000 "$MINIO_ROOT_USER" "$MINIO_ROOT_PASSWORD"; do
            echo "MinIO还未就绪，等待5秒..."
            sleep 5
          done

          echo "创建存储桶..."
          mc mb --ignore-existing myminio/models
          mc mb --ignore-existing myminio/textures
          mc mb --ignore-existing myminio/audio
          mc mb --ignore-existing myminio/renders
          mc mb --ignore-existing myminio/uploads
          mc mb --ignore-existing myminio/backups

          echo "设置存储桶策略..."
          mc policy set download myminio/renders
          mc policy set public myminio/uploads

          echo "创建访问密钥..."
          mc admin user add myminio "$MINIO_ACCESS_KEY" "$MINIO_SECRET_KEY"

          echo "MinIO初始化完成"
        env:
        - name: MINIO_ROOT_USER
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MINIO_ROOT_USER
        - name: MINIO_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MINIO_ROOT_PASSWORD
        - name: MINIO_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MINIO_ACCESS_KEY
        - name: MINIO_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MINIO_SECRET_KEY
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
      restartPolicy: OnFailure
  backoffLimit: 5
  activeDeadlineSeconds: 300
```

应用MinIO部署：

```bash
# 部署MinIO
kubectl apply -f minio-deployment.yaml

# 等待MinIO启动
kubectl wait --for=condition=ready pod -l app=minio -n dl-engine --timeout=300s

# 初始化MinIO存储桶
kubectl apply -f minio-init-job.yaml

# 检查初始化Job状态
kubectl get jobs -n dl-engine
kubectl logs -f job/minio-init -n dl-engine

# 验证MinIO部署
kubectl get pods -n dl-engine -l component=storage
kubectl get svc -n dl-engine -l component=storage
```

### 配置ConfigMap和Secret

创建ConfigMap存储配置信息：

```yaml
# dl-engine-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: dl-engine-config
  namespace: dl-engine
  labels:
    app: dl-engine
    component: config
data:
  # 基础配置
  NODE_ENV: "production"
  CORS_ORIGIN: "*"

  # 服务注册中心配置
  SERVICE_REGISTRY_HOST: "service-registry"
  SERVICE_REGISTRY_PORT: "3010"
  SERVICE_REGISTRY_HTTP_PORT: "4010"

  # 微服务配置
  USER_SERVICE_HOST: "user-service"
  USER_SERVICE_PORT: "3001"
  USER_SERVICE_HTTP_PORT: "4001"
  PROJECT_SERVICE_HOST: "project-service"
  PROJECT_SERVICE_PORT: "3002"
  PROJECT_SERVICE_HTTP_PORT: "4002"
  ASSET_SERVICE_HOST: "asset-service"
  ASSET_SERVICE_PORT: "3003"
  ASSET_SERVICE_HTTP_PORT: "4003"
  RENDER_SERVICE_HOST: "render-service"
  RENDER_SERVICE_PORT: "3004"
  RENDER_SERVICE_HTTP_PORT: "4004"

  # 协作服务配置
  COLLABORATION_SERVICE_1_PORT: "3005"
  COLLABORATION_SERVICE_2_PORT: "3006"
  COLLABORATION_LOAD_BALANCER_PORT: "3007"

  # 游戏服务器配置
  GAME_SERVER_HOST: "game-server"
  GAME_SERVER_PORT: "3030"
  GAME_SERVER_MICROSERVICE_PORT: "3003"

  # 数据库配置
  DB_HOST: "mysql"
  DB_PORT: "3306"
  DB_USERNAME: "root"

  # Redis配置
  REDIS_HOST: "redis"
  REDIS_PORT: "6379"

  # MinIO配置
  MINIO_HOST: "minio"
  MINIO_PORT: "9000"
  MINIO_CONSOLE_PORT: "9001"
  MINIO_USE_SSL: "false"
  MINIO_BUCKET_MODELS: "models"
  MINIO_BUCKET_TEXTURES: "textures"
  MINIO_BUCKET_AUDIO: "audio"
  MINIO_BUCKET_RENDERS: "renders"

  # 文件上传配置
  MAX_FILE_SIZE: "104857600"
  UPLOAD_PATH: "/app/uploads"

  # JWT配置
  JWT_EXPIRES_IN: "1d"

  # 协作服务配置
  ENABLE_COMPRESSION: "true"
  COMPRESSION_LEVEL: "6"
  MAX_BATCH_SIZE: "50"
  MAX_BATCH_WAIT_TIME: "50"

  # API网关配置
  API_GATEWAY_PORT: "3000"

  # 监控配置
  MONITORING_SERVICE_PORT: "3100"
  PROMETHEUS_PORT: "9090"
  GRAFANA_PORT: "3000"
  ELASTICSEARCH_PORT: "9200"
  KIBANA_PORT: "5601"
```

创建Secret存储敏感信息：

```yaml
# dl-engine-secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: dl-engine-secrets
  namespace: dl-engine
  labels:
    app: dl-engine
    component: secrets
type: Opaque
data:
  # 数据库密码（需要base64编码）
  MYSQL_ROOT_PASSWORD: <base64编码的MySQL root密码>
  MYSQL_PASSWORD: <base64编码的MySQL用户密码>

  # JWT密钥
  JWT_SECRET: <base64编码的JWT密钥>

  # MinIO凭据
  MINIO_ROOT_USER: <base64编码的MinIO管理员用户名>
  MINIO_ROOT_PASSWORD: <base64编码的MinIO管理员密码>
  MINIO_ACCESS_KEY: <base64编码的MinIO访问密钥>
  MINIO_SECRET_KEY: <base64编码的MinIO秘密密钥>

  # Redis密码
  REDIS_PASSWORD: <base64编码的Redis密码>

  # 监控配置
  GRAFANA_ADMIN_USER: <base64编码的Grafana管理员用户名>
  GRAFANA_ADMIN_PASSWORD: <base64编码的Grafana管理员密码>

  # 邮件配置（用于告警）
  MAIL_HOST: <base64编码的邮件服务器地址>
  MAIL_PORT: <base64编码的邮件服务器端口>
  MAIL_USER: <base64编码的邮件用户名>
  MAIL_PASS: <base64编码的邮件密码>
  MAIL_FROM: <base64编码的发件人地址>
```

生成密码和密钥的示例脚本：

```bash
# 生成密码和密钥
#!/bin/bash

# 生成强密码
MYSQL_ROOT_PASSWORD=$(openssl rand -base64 32)
MYSQL_PASSWORD=$(openssl rand -base64 32)
JWT_SECRET=$(openssl rand -hex 64)
MINIO_ROOT_USER="dl_engine_admin"
MINIO_ROOT_PASSWORD=$(openssl rand -base64 32)
MINIO_ACCESS_KEY=$(openssl rand -base64 20)
MINIO_SECRET_KEY=$(openssl rand -base64 40)
REDIS_PASSWORD=$(openssl rand -base64 32)
GRAFANA_ADMIN_PASSWORD=$(openssl rand -base64 16)

# 输出base64编码的值
echo "MYSQL_ROOT_PASSWORD: $(echo -n $MYSQL_ROOT_PASSWORD | base64)"
echo "MYSQL_PASSWORD: $(echo -n $MYSQL_PASSWORD | base64)"
echo "JWT_SECRET: $(echo -n $JWT_SECRET | base64)"
echo "MINIO_ROOT_USER: $(echo -n $MINIO_ROOT_USER | base64)"
echo "MINIO_ROOT_PASSWORD: $(echo -n $MINIO_ROOT_PASSWORD | base64)"
echo "MINIO_ACCESS_KEY: $(echo -n $MINIO_ACCESS_KEY | base64)"
echo "MINIO_SECRET_KEY: $(echo -n $MINIO_SECRET_KEY | base64)"
echo "REDIS_PASSWORD: $(echo -n $REDIS_PASSWORD | base64)"
echo "GRAFANA_ADMIN_USER: $(echo -n admin | base64)"
echo "GRAFANA_ADMIN_PASSWORD: $(echo -n $GRAFANA_ADMIN_PASSWORD | base64)"
```

应用配置：

```bash
# 应用配置文件
kubectl apply -f dl-engine-config.yaml
kubectl apply -f dl-engine-secrets.yaml

# 验证配置
kubectl get configmap -n dl-engine
kubectl get secret -n dl-engine
```

### 部署数据库和Redis

首先创建MySQL初始化脚本的ConfigMap：

```yaml
# mysql-init-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: mysql-init-scripts
  namespace: dl-engine
  labels:
    app: mysql
    component: init-scripts
data:
  01-create-databases.sql: |
    -- 创建各个微服务的数据库
    CREATE DATABASE IF NOT EXISTS dl_engine_users CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    CREATE DATABASE IF NOT EXISTS dl_engine_projects CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    CREATE DATABASE IF NOT EXISTS dl_engine_assets CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    CREATE DATABASE IF NOT EXISTS dl_engine_render CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    CREATE DATABASE IF NOT EXISTS dl_engine_registry CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    CREATE DATABASE IF NOT EXISTS dl_engine_monitoring CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

    -- 创建用户并授权
    CREATE USER IF NOT EXISTS 'dl_user'@'%' IDENTIFIED BY 'dl_user_password';
    GRANT ALL PRIVILEGES ON dl_engine_users.* TO 'dl_user'@'%';
    GRANT ALL PRIVILEGES ON dl_engine_projects.* TO 'dl_user'@'%';
    GRANT ALL PRIVILEGES ON dl_engine_assets.* TO 'dl_user'@'%';
    GRANT ALL PRIVILEGES ON dl_engine_render.* TO 'dl_user'@'%';
    GRANT ALL PRIVILEGES ON dl_engine_registry.* TO 'dl_user'@'%';
    GRANT ALL PRIVILEGES ON dl_engine_monitoring.* TO 'dl_user'@'%';
    FLUSH PRIVILEGES;

  02-optimize-config.sql: |
    -- MySQL性能优化配置
    SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB
    SET GLOBAL max_connections = 200;
    SET GLOBAL innodb_log_file_size = 268435456; -- 256MB
    SET GLOBAL query_cache_size = 67108864; -- 64MB
    SET GLOBAL query_cache_type = 1;
```

部署MySQL数据库：

```yaml
# mysql-deployment.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mysql
  namespace: dl-engine
  labels:
    app: mysql
    component: database
spec:
  serviceName: mysql
  replicas: 1
  selector:
    matchLabels:
      app: mysql
  template:
    metadata:
      labels:
        app: mysql
        component: database
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        ports:
        - containerPort: 3306
          name: mysql
        env:
        - name: MYSQL_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MYSQL_ROOT_PASSWORD
        - name: MYSQL_DATABASE
          value: dl_engine
        - name: MYSQL_ALLOW_EMPTY_PASSWORD
          value: "no"
        volumeMounts:
        - name: mysql-data
          mountPath: /var/lib/mysql
        - name: init-scripts
          mountPath: /docker-entrypoint-initdb.d
        - name: mysql-config
          mountPath: /etc/mysql/conf.d
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          exec:
            command:
            - mysqladmin
            - ping
            - -h
            - localhost
            - -u
            - root
            - -p$(MYSQL_ROOT_PASSWORD)
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
        readinessProbe:
          exec:
            command:
            - mysqladmin
            - ping
            - -h
            - localhost
            - -u
            - root
            - -p$(MYSQL_ROOT_PASSWORD)
          initialDelaySeconds: 15
          periodSeconds: 5
          timeoutSeconds: 3
      volumes:
      - name: init-scripts
        configMap:
          name: mysql-init-scripts
      - name: mysql-config
        configMap:
          name: mysql-config
  volumeClaimTemplates:
  - metadata:
      name: mysql-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: "fast-ssd"  # 根据您的存储类调整
      resources:
        requests:
          storage: 50Gi
---
apiVersion: v1
kind: Service
metadata:
  name: mysql
  namespace: dl-engine
  labels:
    app: mysql
    component: database
spec:
  ports:
  - port: 3306
    targetPort: 3306
    name: mysql
  selector:
    app: mysql
  clusterIP: None
---
# MySQL配置文件
apiVersion: v1
kind: ConfigMap
metadata:
  name: mysql-config
  namespace: dl-engine
  labels:
    app: mysql
    component: config
data:
  my.cnf: |
    [mysqld]
    # 基础配置
    max_connections = 200
    innodb_buffer_pool_size = 1G
    innodb_log_file_size = 256M
    innodb_flush_log_at_trx_commit = 2

    # 查询缓存
    query_cache_type = 1
    query_cache_size = 64M

    # 字符集配置
    character-set-server = utf8mb4
    collation-server = utf8mb4_unicode_ci

    # 慢查询日志
    slow_query_log = 1
    slow_query_log_file = /var/log/mysql/slow.log
    long_query_time = 2

    # 二进制日志
    log-bin = mysql-bin
    binlog_format = ROW
    expire_logs_days = 7
```

部署Redis：

```yaml
# redis-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: dl-engine
  labels:
    app: redis
    component: cache
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
        component: cache
    spec:
      containers:
      - name: redis
        image: redis:7.0-alpine
        ports:
        - containerPort: 6379
          name: redis
        command:
        - redis-server
        - /etc/redis/redis.conf
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: REDIS_PASSWORD
        volumeMounts:
        - name: redis-data
          mountPath: /data
        - name: redis-config
          mountPath: /etc/redis
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-pvc
      - name: redis-config
        configMap:
          name: redis-config
---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: dl-engine
  labels:
    app: redis
    component: cache
spec:
  ports:
  - port: 6379
    targetPort: 6379
    name: redis
  selector:
    app: redis
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: dl-engine
  labels:
    app: redis
    component: cache
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: "fast-ssd"  # 根据您的存储类调整
  resources:
    requests:
      storage: 10Gi
---
# Redis配置文件
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  namespace: dl-engine
  labels:
    app: redis
    component: config
data:
  redis.conf: |
    # 基础配置
    bind 0.0.0.0
    port 6379
    protected-mode yes
    requirepass ${REDIS_PASSWORD}

    # 持久化配置
    save 900 1
    save 300 10
    save 60 10000

    # 内存配置
    maxmemory 512mb
    maxmemory-policy allkeys-lru

    # 日志配置
    loglevel notice
    logfile ""

    # 客户端配置
    timeout 300
    tcp-keepalive 300

    # 数据库配置
    databases 16

    # AOF配置
    appendonly yes
    appendfilename "appendonly.aof"
    appendfsync everysec

    # 慢查询日志
    slowlog-log-slower-than 10000
    slowlog-max-len 128
```

应用部署：

```bash
# 应用MySQL配置和部署
kubectl apply -f mysql-init-configmap.yaml
kubectl apply -f mysql-deployment.yaml

# 应用Redis配置和部署
kubectl apply -f redis-deployment.yaml

# 等待数据库和Redis启动
kubectl wait --for=condition=ready pod -l app=mysql -n dl-engine --timeout=300s
kubectl wait --for=condition=ready pod -l app=redis -n dl-engine --timeout=300s

# 验证部署状态
kubectl get pods -n dl-engine -l component=database
kubectl get pods -n dl-engine -l component=cache
kubectl get pvc -n dl-engine
```

### 部署服务注册中心

```yaml
# service-registry-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: service-registry
  namespace: dl-engine
spec:
  replicas: 1
  selector:
    matchLabels:
      app: service-registry
  template:
    metadata:
      labels:
        app: service-registry
    spec:
      containers:
      - name: service-registry
        image: <您的容器仓库>/dl-engine-service-registry:latest
        ports:
        - containerPort: 3010
          name: tcp
        - containerPort: 4010
          name: http
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: NODE_ENV
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: DB_PORT
        - name: DB_USERNAME
          value: root
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MYSQL_ROOT_PASSWORD
        - name: DB_DATABASE
          value: dl_engine_registry
        - name: SERVICE_REGISTRY_HOST
          value: service-registry
        - name: SERVICE_REGISTRY_PORT
          value: "3010"
        - name: SERVICE_REGISTRY_HTTP_PORT
          value: "4010"
        readinessProbe:
          httpGet:
            path: /health
            port: 4010
          initialDelaySeconds: 15
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health
            port: 4010
          initialDelaySeconds: 30
          periodSeconds: 20
---
apiVersion: v1
kind: Service
metadata:
  name: service-registry
  namespace: dl-engine
spec:
  ports:
  - port: 3010
    targetPort: 3010
    name: tcp
  - port: 4010
    targetPort: 4010
    name: http
  selector:
    app: service-registry
```

应用部署：

```bash
kubectl apply -f service-registry-deployment.yaml
```

### 部署微服务

部署用户服务：

```yaml
# user-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  namespace: dl-engine
spec:
  replicas: 2
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
    spec:
      containers:
      - name: user-service
        image: <您的容器仓库>/dl-engine-user-service:latest
        ports:
        - containerPort: 3001
          name: tcp
        - containerPort: 4001
          name: http
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: NODE_ENV
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: DB_PORT
        - name: DB_USERNAME
          value: root
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MYSQL_ROOT_PASSWORD
        - name: DB_DATABASE
          value: dl_engine_users
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: JWT_SECRET
        - name: JWT_EXPIRES_IN
          value: "1d"
        - name: USER_SERVICE_HOST
          value: user-service
        - name: USER_SERVICE_PORT
          value: "3001"
        - name: USER_SERVICE_HTTP_PORT
          value: "4001"
        - name: SERVICE_REGISTRY_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_HOST
        - name: SERVICE_REGISTRY_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_PORT
        readinessProbe:
          httpGet:
            path: /health
            port: 4001
          initialDelaySeconds: 15
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health
            port: 4001
          initialDelaySeconds: 30
          periodSeconds: 20
---
apiVersion: v1
kind: Service
metadata:
  name: user-service
  namespace: dl-engine
spec:
  ports:
  - port: 3001
    targetPort: 3001
    name: tcp
  - port: 4001
    targetPort: 4001
    name: http
  selector:
    app: user-service
```

部署项目服务：

```yaml
# project-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: project-service
  namespace: dl-engine
spec:
  replicas: 2
  selector:
    matchLabels:
      app: project-service
  template:
    metadata:
      labels:
        app: project-service
    spec:
      containers:
      - name: project-service
        image: <您的容器仓库>/dl-engine-project-service:latest
        ports:
        - containerPort: 3002
          name: tcp
        - containerPort: 4002
          name: http
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: NODE_ENV
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: DB_PORT
        - name: DB_USERNAME
          value: root
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MYSQL_ROOT_PASSWORD
        - name: DB_DATABASE
          value: dl_engine_projects
        - name: PROJECT_SERVICE_HOST
          value: project-service
        - name: PROJECT_SERVICE_PORT
          value: "3002"
        - name: PROJECT_SERVICE_HTTP_PORT
          value: "4002"
        - name: SERVICE_REGISTRY_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_HOST
        - name: SERVICE_REGISTRY_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_PORT
        - name: USER_SERVICE_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: USER_SERVICE_HOST
        - name: USER_SERVICE_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: USER_SERVICE_PORT
        readinessProbe:
          httpGet:
            path: /health
            port: 4002
          initialDelaySeconds: 15
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health
            port: 4002
          initialDelaySeconds: 30
          periodSeconds: 20
---
apiVersion: v1
kind: Service
metadata:
  name: project-service
  namespace: dl-engine
spec:
  ports:
  - port: 3002
    targetPort: 3002
    name: tcp
  - port: 4002
    targetPort: 4002
    name: http
  selector:
    app: project-service
```

部署资产服务：

```yaml
# asset-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: asset-service
  namespace: dl-engine
spec:
  replicas: 2
  selector:
    matchLabels:
      app: asset-service
  template:
    metadata:
      labels:
        app: asset-service
    spec:
      containers:
      - name: asset-service
        image: <您的容器仓库>/dl-engine-asset-service:latest
        ports:
        - containerPort: 3003
          name: tcp
        - containerPort: 4003
          name: http
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: NODE_ENV
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: DB_PORT
        - name: DB_USERNAME
          value: root
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MYSQL_ROOT_PASSWORD
        - name: DB_DATABASE
          value: dl_engine_assets
        - name: ASSET_SERVICE_HOST
          value: asset-service
        - name: ASSET_SERVICE_PORT
          value: "3003"
        - name: ASSET_SERVICE_HTTP_PORT
          value: "4003"
        - name: SERVICE_REGISTRY_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_HOST
        - name: SERVICE_REGISTRY_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_PORT
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: JWT_SECRET
        - name: MAX_FILE_SIZE
          value: "104857600"
        - name: MINIO_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: MINIO_HOST
        - name: MINIO_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: MINIO_PORT
        - name: MINIO_USE_SSL
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: MINIO_USE_SSL
        - name: MINIO_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MINIO_ACCESS_KEY
        - name: MINIO_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MINIO_SECRET_KEY
        - name: MINIO_BUCKET_MODELS
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: MINIO_BUCKET_MODELS
        - name: MINIO_BUCKET_TEXTURES
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: MINIO_BUCKET_TEXTURES
        - name: MINIO_BUCKET_AUDIO
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: MINIO_BUCKET_AUDIO
        readinessProbe:
          httpGet:
            path: /health
            port: 4003
          initialDelaySeconds: 15
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health
            port: 4003
          initialDelaySeconds: 30
          periodSeconds: 20
      # 不再需要本地存储卷，因为使用MinIO存储资产
---
apiVersion: v1
kind: Service
metadata:
  name: asset-service
  namespace: dl-engine
spec:
  ports:
  - port: 3003
    targetPort: 3003
    name: tcp
  - port: 4003
    targetPort: 4003
    name: http
  selector:
    app: asset-service

```

部署渲染服务：

```yaml
# render-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: render-service
  namespace: dl-engine
spec:
  replicas: 2
  selector:
    matchLabels:
      app: render-service
  template:
    metadata:
      labels:
        app: render-service
    spec:
      containers:
      - name: render-service
        image: <您的容器仓库>/dl-engine-render-service:latest
        ports:
        - containerPort: 3004
          name: tcp
        - containerPort: 4004
          name: http
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: NODE_ENV
        - name: RENDER_SERVICE_HOST
          value: render-service
        - name: RENDER_SERVICE_PORT
          value: "3004"
        - name: RENDER_SERVICE_HTTP_PORT
          value: "4004"
        - name: SERVICE_REGISTRY_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_HOST
        - name: SERVICE_REGISTRY_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_PORT
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: REDIS_PORT
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: JWT_SECRET
        - name: MINIO_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: MINIO_HOST
        - name: MINIO_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: MINIO_PORT
        - name: MINIO_USE_SSL
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: MINIO_USE_SSL
        - name: MINIO_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MINIO_ACCESS_KEY
        - name: MINIO_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MINIO_SECRET_KEY
        - name: MINIO_BUCKET_RENDERS
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: MINIO_BUCKET_RENDERS
        readinessProbe:
          httpGet:
            path: /health
            port: 4004
          initialDelaySeconds: 15
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health
            port: 4004
          initialDelaySeconds: 30
          periodSeconds: 20
      # 不再需要本地存储卷，因为使用MinIO存储渲染输出
---
apiVersion: v1
kind: Service
metadata:
  name: render-service
  namespace: dl-engine
spec:
  ports:
  - port: 3004
    targetPort: 3004
    name: tcp
  - port: 4004
    targetPort: 4004
    name: http
  selector:
    app: render-service

```

应用部署：

```bash
kubectl apply -f user-service-deployment.yaml
kubectl apply -f project-service-deployment.yaml
kubectl apply -f asset-service-deployment.yaml
kubectl apply -f render-service-deployment.yaml
```

### 部署API网关

```yaml
# api-gateway-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  namespace: dl-engine
spec:
  replicas: 2
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
    spec:
      containers:
      - name: api-gateway
        image: <您的容器仓库>/dl-engine-api-gateway:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: NODE_ENV
        - name: PORT
          value: "3000"
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: JWT_SECRET
        - name: JWT_EXPIRES_IN
          value: "1d"
        - name: USER_SERVICE_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: USER_SERVICE_HOST
        - name: USER_SERVICE_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: USER_SERVICE_PORT
        - name: PROJECT_SERVICE_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: PROJECT_SERVICE_HOST
        - name: PROJECT_SERVICE_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: PROJECT_SERVICE_PORT
        - name: ASSET_SERVICE_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: ASSET_SERVICE_HOST
        - name: ASSET_SERVICE_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: ASSET_SERVICE_PORT
        - name: RENDER_SERVICE_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: RENDER_SERVICE_HOST
        - name: RENDER_SERVICE_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: RENDER_SERVICE_PORT
        - name: SERVICE_REGISTRY_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_HOST
        - name: SERVICE_REGISTRY_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_PORT
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 15
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 20
---
apiVersion: v1
kind: Service
metadata:
  name: api-gateway
  namespace: dl-engine
spec:
  ports:
  - port: 3000
    targetPort: 3000
  selector:
    app: api-gateway
```

### 部署前端应用

```yaml
# editor-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: editor
  namespace: dl-engine
spec:
  replicas: 2
  selector:
    matchLabels:
      app: editor
  template:
    metadata:
      labels:
        app: editor
    spec:
      containers:
      - name: editor
        image: <您的容器仓库>/dl-engine-editor:latest
        ports:
        - containerPort: 80
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 20
          periodSeconds: 15
---
apiVersion: v1
kind: Service
metadata:
  name: editor
  namespace: dl-engine
spec:
  ports:
  - port: 80
    targetPort: 80
  selector:
    app: editor
```

应用部署：

```bash
kubectl apply -f api-gateway-deployment.yaml
kubectl apply -f editor-deployment.yaml
```

## 服务暴露和访问

使用Ingress暴露服务：

```yaml
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dl-engine-ingress
  namespace: dl-engine
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
spec:
  rules:
  - host: dl-engine.example.com  # 替换为您的域名
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: api-gateway
            port:
              number: 3000
      - path: /minio
        pathType: Prefix
        backend:
          service:
            name: minio
            port:
              number: 9000
      - path: /minio-console
        pathType: Prefix
        backend:
          service:
            name: minio
            port:
              number: 9001
      - path: /
        pathType: Prefix
        backend:
          service:
            name: editor
            port:
              number: 80
```

应用Ingress配置：

```bash
kubectl apply -f ingress.yaml
```

## 监控和日志

### 部署Prometheus和Grafana监控

推荐使用Prometheus Operator和Grafana来监控Kubernetes集群和DL（Digital Learning）引擎服务。

```bash
# 安装Prometheus Operator
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update
helm install prometheus prometheus-community/kube-prometheus-stack --namespace monitoring --create-namespace
```

### 配置日志收集

推荐使用EFK(Elasticsearch, Fluentd, Kibana)或ELK(Elasticsearch, Logstash, Kibana)堆栈收集和分析日志。

```bash
# 安装EFK堆栈
helm repo add elastic https://helm.elastic.co
helm repo update
helm install elasticsearch elastic/elasticsearch --namespace logging --create-namespace
helm install kibana elastic/kibana --namespace logging
helm install fluentd fluent/fluentd --namespace logging
```

## 扩展和高可用性

### 水平自动扩展

为微服务配置HPA(Horizontal Pod Autoscaler)：

```yaml
# hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-gateway-hpa
  namespace: dl-engine
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-gateway
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: user-service-hpa
  namespace: dl-engine
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: user-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

应用HPA配置：

```bash
kubectl apply -f hpa.yaml
```

## 故障排除

### 常见问题

1. **服务无法启动**
   - 检查环境变量配置是否正确
   - 检查数据库连接是否正常
   - 查看服务日志：`kubectl logs -f deployment/service-name -n dl-engine`

2. **服务注册失败**
   - 确保服务注册中心正常运行
   - 检查网络连接和DNS解析
   - 查看服务注册中心日志

3. **数据库连接问题**
   - 确保数据库Pod正常运行
   - 检查数据库凭据是否正确
   - 检查数据库初始化脚本是否执行

4. **存储问题**
   - 确保PV和PVC正确配置
   - 检查存储类是否支持所需的访问模式
   - 检查存储容量是否足够

5. **MinIO对象存储问题**
   - 确保MinIO服务正常运行：`kubectl get pods -n dl-engine | grep minio`
   - 检查MinIO存储桶是否已创建：`kubectl exec -it <minio-pod-name> -n dl-engine -- mc ls myminio`
   - 检查MinIO凭据是否正确配置
   - 验证微服务是否能正确连接MinIO：`kubectl logs -f <service-pod-name> -n dl-engine | grep minio`

### 查看日志

```bash
# 查看特定服务的日志
kubectl logs -f deployment/api-gateway -n dl-engine

# 查看特定Pod的日志
kubectl logs -f pod-name -n dl-engine

# 查看前一个容器实例的日志
kubectl logs -f pod-name -n dl-engine --previous
```

### 进入容器调试

```bash
# 进入容器执行命令
kubectl exec -it pod-name -n dl-engine -- /bin/sh

# 查看容器环境变量
kubectl exec pod-name -n dl-engine -- env
```
