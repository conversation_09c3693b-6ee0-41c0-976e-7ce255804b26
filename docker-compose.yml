version: '3.8'

services:
  # 数据库
  mysql:
    image: mysql:8.0
    container_name: dl-engine-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_ALLOW_EMPTY_PASSWORD: 'no'
      MYSQL_DATABASE: dl_engine
    ports:
      - '3306:3306'
    volumes:
      - mysql_data:/var/lib/mysql
      - ./server/shared/init-scripts:/docker-entrypoint-initdb.d
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'mysqladmin', 'ping', '-h', 'localhost', '-u', 'root', '-p${MYSQL_ROOT_PASSWORD}']
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存
  redis:
    image: redis:7.0-alpine
    container_name: dl-engine-redis
    restart: always
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 10s
      timeout: 5s
      retries: 5

  # 服务注册中心
  service-registry:
    build:
      context: ./server/service-registry
      dockerfile: Dockerfile
    container_name: dl-engine-service-registry
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=dl_engine_registry
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - SERVICE_REGISTRY_HTTP_PORT=4010
    ports:
      - '3010:3010'
      - '4010:4010'
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4010/api/health']
      interval: 30s
      timeout: 10s
      retries: 5

  # API网关
  api-gateway:
    build:
      context: ./server/api-gateway
      dockerfile: Dockerfile
    container_name: dl-engine-api-gateway
    restart: always
    depends_on:
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3000
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=1d
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - ASSET_SERVICE_HOST=asset-service
      - ASSET_SERVICE_PORT=3003
      - RENDER_SERVICE_HOST=render-service
      - RENDER_SERVICE_PORT=3004
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
    ports:
      - '3000:3000'
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/api/health']
      interval: 10s
      timeout: 5s
      retries: 5

  # 用户服务
  user-service:
    build:
      context: ./server/user-service
      dockerfile: Dockerfile
    container_name: dl-engine-user-service
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=dl_engine_users
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=1d
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - USER_SERVICE_HTTP_PORT=4001
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
    ports:
      - '3001:3001'
      - '4001:4001'
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4001/health']
      interval: 10s
      timeout: 5s
      retries: 5

  # 项目服务
  project-service:
    build:
      context: ./server/project-service
      dockerfile: Dockerfile
    container_name: dl-engine-project-service
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=dl_engine_projects
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - PROJECT_SERVICE_HTTP_PORT=4002
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
    ports:
      - '3002:3002'
      - '4002:4002'
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4002/health']
      interval: 10s
      timeout: 5s
      retries: 5

  # 资产服务
  asset-service:
    build:
      context: ./server/asset-service
      dockerfile: Dockerfile
    container_name: dl-engine-asset-service
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=dl_engine_assets
      - ASSET_SERVICE_HOST=asset-service
      - ASSET_SERVICE_PORT=3003
      - ASSET_SERVICE_HTTP_PORT=4003
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - JWT_SECRET=${JWT_SECRET}
      - MAX_FILE_SIZE=104857600
    ports:
      - '3003:3003'
      - '4003:4003'
    volumes:
      - asset_uploads:/app/uploads
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4003/health']
      interval: 10s
      timeout: 5s
      retries: 5

  # 渲染服务
  render-service:
    build:
      context: ./server/render-service
      dockerfile: Dockerfile
    container_name: dl-engine-render-service
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=dl_engine_render
      - RENDER_SERVICE_HOST=render-service
      - RENDER_SERVICE_PORT=3004
      - RENDER_SERVICE_HTTP_PORT=4004
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - ASSET_SERVICE_HOST=asset-service
      - ASSET_SERVICE_PORT=3003
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    ports:
      - '3004:3004'
      - '4004:4004'
    volumes:
      - render_outputs:/app/renders
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4004/health']
      interval: 10s
      timeout: 5s
      retries: 5

  # 协作服务（主实例）
  collaboration-service-1:
    build:
      context: ./server/collaboration-service
      dockerfile: Dockerfile
    container_name: dl-engine-collaboration-service-1
    restart: always
    depends_on:
      redis:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3005
      - INSTANCE_ID=collaboration-1
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - ENABLE_COMPRESSION=true
      - COMPRESSION_LEVEL=6
      - MAX_BATCH_SIZE=50
      - MAX_BATCH_WAIT_TIME=50
    ports:
      - '3005:3005'
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'wget', '-qO-', 'http://localhost:3005/health']
      interval: 10s
      timeout: 5s
      retries: 3

  # 协作服务（副本实例）
  collaboration-service-2:
    build:
      context: ./server/collaboration-service
      dockerfile: Dockerfile
    container_name: dl-engine-collaboration-service-2
    restart: always
    depends_on:
      redis:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3006
      - INSTANCE_ID=collaboration-2
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - ENABLE_COMPRESSION=true
      - COMPRESSION_LEVEL=6
      - MAX_BATCH_SIZE=50
      - MAX_BATCH_WAIT_TIME=50
    ports:
      - '3006:3006'
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'wget', '-qO-', 'http://localhost:3006/health']
      interval: 10s
      timeout: 5s
      retries: 3

  # 协作服务负载均衡
  collaboration-load-balancer:
    image: nginx:alpine
    container_name: dl-engine-collaboration-lb
    restart: always
    depends_on:
      collaboration-service-1:
        condition: service_healthy
      collaboration-service-2:
        condition: service_healthy
    volumes:
      - ./server/collaboration-service/nginx.conf:/etc/nginx/conf.d/default.conf
    ports:
      - '3007:80'
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'wget', '-qO-', 'http://localhost:80/health']
      interval: 10s
      timeout: 5s
      retries: 3

  # 编辑器前端
  editor:
    build:
      context: .
      dockerfile: editor/Dockerfile
    container_name: dl-engine-editor
    restart: always
    depends_on:
      api-gateway:
        condition: service_healthy
      collaboration-load-balancer:
        condition: service_healthy
    environment:
      - REACT_APP_API_URL=http://localhost:3000/api
      - REACT_APP_COLLABORATION_SERVER_URL=ws://localhost:3007
    ports:
      - '80:80'
    networks:
      - dl-engine-network

networks:
  dl-engine-network:
    driver: bridge

volumes:
  mysql_data:
  redis_data:
  asset_uploads:
  render_outputs:
