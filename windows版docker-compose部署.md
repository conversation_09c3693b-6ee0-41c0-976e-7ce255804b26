# Windows版Docker Compose部署方案

## 项目概述

本项目是一个基于RAG（检索增强生成）的数字人交互系统，采用微服务架构设计，包含：

- **前端编辑器**：基于React + TypeScript的数字人编辑器
- **后端微服务**：包含多个Node.js/NestJS微服务和Python AI服务
- **基础设施**：MySQL、Redis、MinIO、Chroma向量数据库等

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端编辑器     │    │   API网关       │    │   服务注册中心   │
│   (React)       │◄──►│   (NestJS)      │◄──►│   (NestJS)      │
│   Port: 80      │    │   Port: 3000    │    │   Port: 3010    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼──────┐
        │  用户服务     │ │  项目服务    │ │  资产服务   │
        │  Port: 3001  │ │  Port: 3002 │ │ Port: 3003 │
        └──────────────┘ └─────────────┘ └────────────┘
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼──────┐
        │  渲染服务     │ │  协作服务    │ │  AI模型服务 │
        │  Port: 3004  │ │  Port: 3005 │ │ Port: 8002 │
        └──────────────┘ └─────────────┘ └────────────┘
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼──────┐
        │  知识库服务   │ │  RAG引擎     │ │  绑定服务   │
        │  Port: 8008  │ │  Port: 8009 │ │ Port: 3001 │
        └──────────────┘ └─────────────┘ └────────────┘
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼──────┐
        │    MySQL     │ │    Redis    │ │   MinIO    │
        │  Port: 3306  │ │  Port: 6379 │ │ Port: 9000 │
        └──────────────┘ └─────────────┘ └────────────┘
```

## Windows环境前置条件

### 1. 系统要求
- **操作系统**：Windows 10/11 Pro 或 Windows Server 2019/2022
- **内存**：最低8GB，推荐16GB以上
- **存储**：最低50GB可用空间，推荐100GB以上
- **CPU**：4核心以上，推荐8核心

### 2. 必需软件
- **Docker Desktop for Windows** 4.15.0+
- **Git for Windows** 2.40.0+
- **PowerShell** 5.1+ 或 PowerShell Core 7.0+
- **Visual Studio Code**（推荐，用于代码编辑）

### 3. Docker Desktop配置
```powershell
# 启用WSL2后端（推荐）
wsl --install
wsl --set-default-version 2

# 配置Docker Desktop资源限制
# 在Docker Desktop设置中：
# - Memory: 8GB+
# - CPU: 4核心+
# - Disk image size: 64GB+
```

## 部署配置文件

### 1. Windows专用Docker Compose配置

本方案基于现有的`docker-compose.yml`进行Windows优化，主要改进包括：

- **路径映射优化**：使用Windows兼容的路径格式
- **端口配置**：避免Windows保留端口冲突
- **资源限制**：针对Windows环境优化内存和CPU配置
- **健康检查**：使用Windows兼容的健康检查命令
- **网络配置**：优化Windows下的容器网络性能

### 2. 环境变量配置

创建`.env`文件用于Windows环境：

```env
# 数据库配置
MYSQL_ROOT_PASSWORD=your_secure_password_here
DB_HOST=mysql
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_secure_password_here

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password_here

# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=1d

# MinIO配置
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123

# AI服务配置
OPENAI_API_KEY=your_openai_api_key_here
AZURE_OPENAI_ENDPOINT=your_azure_endpoint_here
AZURE_OPENAI_API_KEY=your_azure_api_key_here

# 监控配置
GRAFANA_PASSWORD=admin123
PROMETHEUS_RETENTION=15d

# Windows特定配置
COMPOSE_CONVERT_WINDOWS_PATHS=1
COMPOSE_FORCE_WINDOWS_HOST=1
```

### 3. Windows路径映射配置

在Windows环境下，需要特别注意Docker卷映射的路径格式：

```yaml
volumes:
  # Windows格式路径映射
  - type: bind
    source: ./config/mysql
    target: /etc/mysql/conf.d
  - type: bind
    source: ./logs
    target: /var/log
  # 命名卷（推荐用于数据持久化）
  - mysql_data:/var/lib/mysql
  - redis_data:/data
```

## 核心服务配置

### 1. 数据库服务（MySQL 8.0）
- **用途**：存储用户数据、项目信息、资产元数据等
- **配置**：支持中文字符集，优化Windows下的性能
- **数据持久化**：使用Docker命名卷确保数据安全

### 2. 缓存服务（Redis 7.0）
- **用途**：会话缓存、任务队列、实时协作数据
- **配置**：启用持久化，配置内存限制
- **集群支持**：支持Redis Cluster模式（可选）

### 3. 对象存储（MinIO）
- **用途**：存储用户上传的文件、模型资产、渲染输出
- **配置**：配置访问密钥，启用Web控制台
- **备份策略**：支持自动备份和版本控制

### 4. 向量数据库（Chroma）
- **用途**：存储文档向量嵌入，支持语义搜索
- **配置**：优化检索性能，支持批量操作
- **扩展性**：支持水平扩展和分片

## 微服务详细配置

### 1. API网关服务
- **功能**：统一入口、路由转发、认证授权、限流
- **端口**：3000
- **依赖**：服务注册中心、Redis
- **健康检查**：HTTP GET /api/health

### 2. 用户服务
- **功能**：用户注册、登录、权限管理
- **端口**：3001（gRPC）、4001（HTTP）
- **数据库**：dl_engine_users
- **认证**：JWT Token

### 3. 项目服务
- **功能**：项目创建、管理、版本控制
- **端口**：3002（gRPC）、4002（HTTP）
- **数据库**：dl_engine_projects
- **文件存储**：MinIO

### 4. 资产服务
- **功能**：3D模型、纹理、动画资产管理
- **端口**：3003（gRPC）、4003（HTTP）
- **数据库**：dl_engine_assets
- **文件存储**：MinIO + 本地缓存

### 5. 渲染服务
- **功能**：场景渲染、图像生成、视频导出
- **端口**：3004（gRPC）、4004（HTTP）
- **数据库**：dl_engine_render
- **GPU支持**：可选NVIDIA GPU加速

### 6. 协作服务
- **功能**：实时协作、WebSocket连接、状态同步
- **端口**：3005、3006（多实例）
- **负载均衡**：Nginx upstream
- **消息队列**：Redis Pub/Sub

### 7. AI模型服务
- **功能**：AI模型推理、文本生成、图像处理
- **端口**：8002
- **技术栈**：Python + FastAPI + PyTorch
- **GPU支持**：CUDA 11.8+

### 8. 知识库服务
- **功能**：文档上传、解析、向量化
- **端口**：8008
- **向量数据库**：Chroma
- **文件处理**：支持PDF、DOCX、TXT等格式

### 9. RAG引擎
- **功能**：检索增强生成、多知识库查询
- **端口**：8009
- **LLM支持**：OpenAI、Azure OpenAI、本地模型
- **流式响应**：支持Server-Sent Events

## 前端编辑器配置

### 1. 技术栈
- **框架**：React 18 + TypeScript
- **UI库**：Ant Design 5.x
- **状态管理**：Redux Toolkit
- **3D引擎**：Three.js + 自研DL引擎
- **构建工具**：Vite

### 2. 环境配置
```typescript
// 生产环境配置
export const config = {
  API_BASE_URL: 'http://localhost:3000/api',
  WS_URL: 'ws://localhost:3007',
  UPLOAD_MAX_SIZE: 100 * 1024 * 1024, // 100MB
  SUPPORTED_FORMATS: ['.glb', '.gltf', '.fbx', '.obj'],
  COLLABORATION_ENABLED: true,
  REAL_TIME_SYNC: true
};
```

### 3. Docker配置
- **基础镜像**：node:22-alpine
- **构建阶段**：多阶段构建优化镜像大小
- **Web服务器**：Nginx Alpine
- **静态资源**：Gzip压缩、缓存优化

## 监控和日志

### 1. Prometheus监控
- **指标收集**：系统指标、应用指标、业务指标
- **告警规则**：CPU、内存、磁盘、响应时间
- **数据保留**：15天（可配置）

### 2. Grafana可视化
- **仪表板**：系统概览、服务状态、性能分析
- **告警通知**：邮件、Slack、钉钉
- **用户管理**：RBAC权限控制

### 3. 日志管理
- **日志格式**：JSON结构化日志
- **日志轮转**：按大小和时间轮转
- **日志聚合**：可选ELK Stack集成

## 安全配置

### 1. 网络安全
- **防火墙**：仅开放必要端口
- **TLS加密**：HTTPS/WSS协议
- **内网隔离**：容器间网络隔离

### 2. 认证授权
- **JWT Token**：无状态认证
- **RBAC权限**：基于角色的访问控制
- **API限流**：防止恶意请求

### 3. 数据安全
- **数据加密**：敏感数据加密存储
- **备份策略**：定期自动备份
- **访问审计**：操作日志记录

## 性能优化

### 1. 容器优化
- **资源限制**：合理分配CPU和内存
- **镜像优化**：多阶段构建、Alpine基础镜像
- **启动优化**：健康检查、依赖管理

### 2. 数据库优化
- **连接池**：配置合适的连接池大小
- **索引优化**：关键字段建立索引
- **查询优化**：避免N+1查询问题

### 3. 缓存策略
- **Redis缓存**：热点数据缓存
- **CDN加速**：静态资源CDN
- **浏览器缓存**：合理设置缓存头

## Windows特定优化

### 1. 文件系统优化
```yaml
# 使用命名卷而非绑定挂载提升性能
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
```

### 2. 网络优化
```yaml
# 使用自定义网络提升性能
networks:
  dl-engine-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

### 3. 内存管理
```yaml
# 为Windows环境优化内存配置
deploy:
  resources:
    limits:
      memory: 2G
    reservations:
      memory: 1G
```

## 部署步骤

### 第一步：环境准备

1. **安装Docker Desktop**
```powershell
# 下载并安装Docker Desktop for Windows
# https://www.docker.com/products/docker-desktop/

# 验证安装
docker --version
docker-compose --version
```

2. **克隆项目代码**
```powershell
# 克隆项目
git clone <repository-url>
cd newsystem

# 检查项目结构
dir
```

3. **配置环境变量**
```powershell
# 复制环境变量模板
copy .env.example .env

# 编辑环境变量（使用记事本或VS Code）
notepad .env
```

### 第二步：创建Windows专用配置

1. **创建Windows Docker Compose文件**
```yaml
# docker-compose.windows.yml
version: '3.8'

services:
  # MySQL数据库 - Windows优化版本
  mysql:
    image: mysql:8.0
    container_name: dl-engine-mysql-win
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_ALLOW_EMPTY_PASSWORD: 'no'
      MYSQL_DATABASE: dl_engine
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - '3306:3306'
    volumes:
      - mysql_data:/var/lib/mysql
      - type: bind
        source: ./server/shared/init-scripts
        target: /docker-entrypoint-initdb.d
        read_only: true
    networks:
      - dl-engine-network
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --max_connections=1000
      --innodb_buffer_pool_size=1G
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
    healthcheck:
      test: ['CMD', 'mysqladmin', 'ping', '-h', 'localhost', '-u', 'root', '-p${MYSQL_ROOT_PASSWORD}']
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Redis缓存 - Windows优化版本
  redis:
    image: redis:7.0-alpine
    container_name: dl-engine-redis-win
    restart: unless-stopped
    command: >
      redis-server
      --appendonly yes
      --maxmemory 1gb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 30s
      timeout: 10s
      retries: 3

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    container_name: dl-engine-minio-win
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ACCESS_KEY}
      MINIO_ROOT_PASSWORD: ${MINIO_SECRET_KEY}
      MINIO_BROWSER_REDIRECT_URL: http://localhost:9001
    ports:
      - '9000:9000'
      - '9001:9001'
    volumes:
      - minio_data:/data
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:9000/minio/health/live']
      interval: 30s
      timeout: 10s
      retries: 3

  # Chroma向量数据库
  chroma:
    image: chromadb/chroma:latest
    container_name: dl-engine-chroma-win
    restart: unless-stopped
    environment:
      CHROMA_SERVER_HOST: 0.0.0.0
      CHROMA_SERVER_HTTP_PORT: 8000
      CHROMA_DB_IMPL: clickhouse
      CLICKHOUSE_HOST: localhost
      CLICKHOUSE_PORT: 8123
    ports:
      - '8000:8000'
    volumes:
      - chroma_data:/chroma/chroma
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:8000/api/v1/heartbeat']
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  dl-engine-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  chroma_data:
    driver: local
  asset_uploads:
    driver: local
  render_outputs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
```

### 第三步：启动基础设施服务

```powershell
# 启动基础设施服务（数据库、缓存等）
docker-compose -f docker-compose.windows.yml up -d mysql redis minio chroma

# 等待服务启动完成
Start-Sleep -Seconds 60

# 检查服务状态
docker-compose -f docker-compose.windows.yml ps
```

### 第四步：启动微服务

```powershell
# 启动服务注册中心
docker-compose -f docker-compose.windows.yml up -d service-registry

# 等待服务注册中心启动
Start-Sleep -Seconds 30

# 启动核心微服务
docker-compose -f docker-compose.windows.yml up -d api-gateway user-service project-service asset-service

# 启动业务微服务
docker-compose -f docker-compose.windows.yml up -d render-service collaboration-service-1 collaboration-service-2

# 启动AI和RAG服务
docker-compose -f docker-compose.windows.yml up -d ai-model-service knowledge-service rag-engine

# 启动负载均衡器
docker-compose -f docker-compose.windows.yml up -d collaboration-load-balancer
```

### 第五步：启动前端编辑器

```powershell
# 启动前端编辑器
docker-compose -f docker-compose.windows.yml up -d editor

# 检查所有服务状态
docker-compose -f docker-compose.windows.yml ps
```

### 第六步：验证部署

```powershell
# 检查服务健康状态
docker-compose -f docker-compose.windows.yml exec api-gateway curl -f http://localhost:3000/api/health

# 检查前端访问
Start-Process "http://localhost"

# 检查MinIO控制台
Start-Process "http://localhost:9001"

# 检查Chroma API
Invoke-RestMethod -Uri "http://localhost:8000/api/v1/heartbeat"
```

## Windows专用启动脚本

### 1. 一键启动脚本 (start-windows.ps1)

```powershell
#!/usr/bin/env pwsh
# Windows Docker Compose 一键启动脚本

param(
    [switch]$Clean,
    [switch]$Build,
    [switch]$Logs,
    [string]$Service = ""
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

# 检查Docker是否运行
function Test-DockerRunning {
    try {
        docker info | Out-Null
        return $true
    } catch {
        return $false
    }
}

# 检查环境文件
function Test-EnvironmentFile {
    if (-not (Test-Path ".env")) {
        Write-Error "环境文件 .env 不存在，请先创建环境配置文件"
        Write-Info "可以复制 .env.example 作为模板："
        Write-Info "Copy-Item .env.example .env"
        exit 1
    }
}

# 主函数
function Main {
    Write-Info "🚀 启动 DL Engine Windows Docker Compose 部署"

    # 检查Docker状态
    if (-not (Test-DockerRunning)) {
        Write-Error "Docker Desktop 未运行，请先启动 Docker Desktop"
        exit 1
    }
    Write-Success "Docker Desktop 运行正常"

    # 检查环境文件
    Test-EnvironmentFile
    Write-Success "环境配置文件检查通过"

    # 清理选项
    if ($Clean) {
        Write-Warning "清理现有容器和卷..."
        docker-compose -f docker-compose.windows.yml down -v --remove-orphans
        docker system prune -f
        Write-Success "清理完成"
    }

    # 构建选项
    if ($Build) {
        Write-Info "构建Docker镜像..."
        docker-compose -f docker-compose.windows.yml build --no-cache
        Write-Success "镜像构建完成"
    }

    # 启动特定服务
    if ($Service) {
        Write-Info "启动服务: $Service"
        docker-compose -f docker-compose.windows.yml up -d $Service
        if ($Logs) {
            docker-compose -f docker-compose.windows.yml logs -f $Service
        }
        return
    }

    # 分阶段启动所有服务
    Write-Info "第1阶段: 启动基础设施服务..."
    docker-compose -f docker-compose.windows.yml up -d mysql redis minio chroma

    Write-Info "等待基础设施服务启动完成..."
    Start-Sleep -Seconds 60

    Write-Info "第2阶段: 启动服务注册中心..."
    docker-compose -f docker-compose.windows.yml up -d service-registry
    Start-Sleep -Seconds 30

    Write-Info "第3阶段: 启动核心微服务..."
    docker-compose -f docker-compose.windows.yml up -d api-gateway user-service project-service asset-service
    Start-Sleep -Seconds 30

    Write-Info "第4阶段: 启动业务微服务..."
    docker-compose -f docker-compose.windows.yml up -d render-service collaboration-service-1 collaboration-service-2
    Start-Sleep -Seconds 30

    Write-Info "第5阶段: 启动AI和RAG服务..."
    docker-compose -f docker-compose.windows.yml up -d ai-model-service knowledge-service rag-engine
    Start-Sleep -Seconds 30

    Write-Info "第6阶段: 启动负载均衡和前端..."
    docker-compose -f docker-compose.windows.yml up -d collaboration-load-balancer editor

    Write-Success "🎉 所有服务启动完成！"

    # 显示服务状态
    Write-Info "📊 服务状态："
    docker-compose -f docker-compose.windows.yml ps

    # 显示访问地址
    Write-Info "🌐 访问地址："
    Write-Info "  前端编辑器: http://localhost"
    Write-Info "  API网关: http://localhost:3000"
    Write-Info "  MinIO控制台: http://localhost:9001"
    Write-Info "  Chroma API: http://localhost:8000"

    # 显示日志选项
    if ($Logs) {
        Write-Info "📋 显示实时日志..."
        docker-compose -f docker-compose.windows.yml logs -f
    } else {
        Write-Info "💡 查看实时日志请运行: .\start-windows.ps1 -Logs"
    }
}

# 执行主函数
Main
```

### 2. 停止脚本 (stop-windows.ps1)

```powershell
#!/usr/bin/env pwsh
# Windows Docker Compose 停止脚本

param(
    [switch]$Clean,
    [string]$Service = ""
)

$ErrorActionPreference = "Stop"

function Write-Info($message) {
    Write-Host "ℹ️  $message" -ForegroundColor Cyan
}

function Write-Success($message) {
    Write-Host "✅ $message" -ForegroundColor Green
}

function Write-Warning($message) {
    Write-Host "⚠️  $message" -ForegroundColor Yellow
}

Write-Info "🛑 停止 DL Engine 服务"

if ($Service) {
    Write-Info "停止服务: $Service"
    docker-compose -f docker-compose.windows.yml stop $Service
} else {
    Write-Info "停止所有服务..."
    docker-compose -f docker-compose.windows.yml down
}

if ($Clean) {
    Write-Warning "清理容器和网络..."
    docker-compose -f docker-compose.windows.yml down -v --remove-orphans
    Write-Success "清理完成"
} else {
    Write-Success "服务已停止"
}

Write-Info "💡 完全清理请运行: .\stop-windows.ps1 -Clean"
```

## 故障排除指南

### 常见问题及解决方案

#### 1. Docker Desktop相关问题

**问题**: Docker Desktop启动失败
```powershell
# 解决方案
# 1. 检查Hyper-V是否启用
Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V

# 2. 检查WSL2是否正确安装
wsl --list --verbose

# 3. 重启Docker Desktop
Stop-Process -Name "Docker Desktop" -Force
Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe"
```

**问题**: 容器启动时出现权限错误
```powershell
# 解决方案：确保Docker Desktop有足够权限
# 1. 以管理员身份运行PowerShell
# 2. 检查Docker守护进程状态
docker info

# 3. 重新启动Docker服务
Restart-Service docker
```

#### 2. 网络连接问题

**问题**: 容器间无法通信
```powershell
# 诊断网络问题
docker network ls
docker network inspect dl-engine-network

# 解决方案：重新创建网络
docker network rm dl-engine-network
docker network create --driver bridge dl-engine-network
```

**问题**: 端口冲突
```powershell
# 检查端口占用
netstat -ano | findstr :3000
netstat -ano | findstr :3306

# 解决方案：修改端口映射或停止冲突进程
# 在 .env 文件中修改端口配置
```

#### 3. 数据库连接问题

**问题**: MySQL连接失败
```powershell
# 检查MySQL容器状态
docker logs dl-engine-mysql-win

# 测试数据库连接
docker exec -it dl-engine-mysql-win mysql -u root -p

# 解决方案：重置数据库密码
docker exec -it dl-engine-mysql-win mysql -u root -p -e "ALTER USER 'root'@'%' IDENTIFIED BY 'new_password';"
```

**问题**: Redis连接失败
```powershell
# 检查Redis状态
docker logs dl-engine-redis-win

# 测试Redis连接
docker exec -it dl-engine-redis-win redis-cli ping

# 解决方案：清理Redis数据
docker exec -it dl-engine-redis-win redis-cli FLUSHALL
```

#### 4. 存储相关问题

**问题**: MinIO无法访问
```powershell
# 检查MinIO状态
docker logs dl-engine-minio-win

# 访问MinIO控制台
Start-Process "http://localhost:9001"

# 解决方案：重新配置MinIO
docker exec -it dl-engine-minio-win mc admin info local
```

**问题**: 文件上传失败
```powershell
# 检查磁盘空间
Get-PSDrive C

# 检查目录权限
Get-Acl ./data/uploads

# 解决方案：清理临时文件
Remove-Item ./temp/* -Recurse -Force
```

#### 5. 服务启动问题

**问题**: 微服务启动失败
```powershell
# 查看服务日志
docker-compose -f docker-compose.windows.yml logs service-name

# 检查服务依赖
docker-compose -f docker-compose.windows.yml ps

# 解决方案：按依赖顺序重启服务
.\stop-windows.ps1 -Service service-name
.\start-windows.ps1 -Service service-name
```

**问题**: 前端编辑器无法访问
```powershell
# 检查前端容器状态
docker logs dl-engine-editor-win

# 检查API连接
Invoke-RestMethod -Uri "http://localhost:3000/api/health"

# 解决方案：重新构建前端
docker-compose -f docker-compose.windows.yml build editor
```

### 性能优化建议

#### 1. Windows系统优化

```powershell
# 优化Docker Desktop设置
# 1. 增加内存分配（推荐8GB+）
# 2. 增加CPU核心数（推荐4核+）
# 3. 启用WSL2后端
# 4. 配置文件共享目录

# 优化Windows性能
# 1. 禁用不必要的启动程序
Get-CimInstance -ClassName Win32_StartupCommand | Select-Object Name, Command

# 2. 清理系统临时文件
Remove-Item $env:TEMP\* -Recurse -Force -ErrorAction SilentlyContinue

# 3. 优化虚拟内存
# 控制面板 -> 系统 -> 高级系统设置 -> 性能设置 -> 虚拟内存
```

#### 2. 容器资源优化

```yaml
# 在 docker-compose.windows.yml 中调整资源限制
deploy:
  resources:
    limits:
      memory: 2G      # 根据实际需求调整
      cpus: '1.0'     # 根据CPU核心数调整
    reservations:
      memory: 1G
      cpus: '0.5'
```

#### 3. 数据库性能优化

```sql
-- MySQL优化配置
-- 在容器启动后执行
SET GLOBAL innodb_buffer_pool_size = **********;  -- 1GB
SET GLOBAL query_cache_size = 134217728;          -- 128MB
SET GLOBAL max_connections = 1000;
```

### 监控和维护

#### 1. 健康检查脚本

```powershell
# health-check-windows.ps1
function Test-ServiceHealth {
    $services = @(
        @{Name="MySQL"; URL="http://localhost:3306"},
        @{Name="Redis"; URL="http://localhost:6379"},
        @{Name="API Gateway"; URL="http://localhost:3000/api/health"},
        @{Name="MinIO"; URL="http://localhost:9000/minio/health/live"},
        @{Name="Chroma"; URL="http://localhost:8000/api/v1/heartbeat"}
    )

    foreach ($service in $services) {
        try {
            $response = Invoke-RestMethod -Uri $service.URL -TimeoutSec 5
            Write-Host "✅ $($service.Name): 健康" -ForegroundColor Green
        } catch {
            Write-Host "❌ $($service.Name): 异常" -ForegroundColor Red
        }
    }
}

Test-ServiceHealth
```

#### 2. 日志管理

```powershell
# 查看所有服务日志
docker-compose -f docker-compose.windows.yml logs

# 查看特定服务日志
docker-compose -f docker-compose.windows.yml logs -f mysql

# 清理日志文件
docker system prune -f
```

#### 3. 备份脚本

```powershell
# backup-windows.ps1
$backupDir = ".\backups\$(Get-Date -Format 'yyyy-MM-dd-HH-mm-ss')"
New-Item -ItemType Directory -Path $backupDir -Force

# 备份数据库
docker exec dl-engine-mysql-win mysqldump -u root -p$env:MYSQL_ROOT_PASSWORD --all-databases > "$backupDir\mysql-backup.sql"

# 备份MinIO数据
Copy-Item ".\data\minio" "$backupDir\minio" -Recurse

# 备份配置文件
Copy-Item ".env" "$backupDir\.env"
Copy-Item "docker-compose.windows.yml" "$backupDir\docker-compose.windows.yml"

Write-Host "备份完成: $backupDir"
```

### 安全配置

#### 1. 防火墙设置

```powershell
# 配置Windows防火墙规则
New-NetFirewallRule -DisplayName "DL Engine HTTP" -Direction Inbound -Protocol TCP -LocalPort 80 -Action Allow
New-NetFirewallRule -DisplayName "DL Engine API" -Direction Inbound -Protocol TCP -LocalPort 3000 -Action Allow
New-NetFirewallRule -DisplayName "DL Engine MinIO" -Direction Inbound -Protocol TCP -LocalPort 9000,9001 -Action Allow
```

#### 2. SSL证书配置

```powershell
# 生成自签名证书（仅用于测试）
$cert = New-SelfSignedCertificate -DnsName "localhost" -CertStoreLocation "cert:\LocalMachine\My"
$certPath = "cert:\LocalMachine\My\$($cert.Thumbprint)"
Export-Certificate -Cert $certPath -FilePath ".\ssl\cert.crt"
```

#### 3. 访问控制

```yaml
# 在 docker-compose.windows.yml 中添加网络隔离
networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
    internal: true  # 内部网络，不允许外部访问
```

## 运维操作指南

### 日常维护任务

#### 1. 每日检查清单

```powershell
# daily-check.ps1
Write-Host "=== DL Engine 每日健康检查 ===" -ForegroundColor Cyan

# 1. 检查容器状态
Write-Host "1. 检查容器状态..." -ForegroundColor Yellow
docker-compose -f docker-compose.windows.yml ps

# 2. 检查磁盘空间
Write-Host "2. 检查磁盘空间..." -ForegroundColor Yellow
Get-PSDrive C | Format-Table Name, @{Name="Used(GB)";Expression={[math]::Round($_.Used/1GB,2)}}, @{Name="Free(GB)";Expression={[math]::Round($_.Free/1GB,2)}}

# 3. 检查内存使用
Write-Host "3. 检查内存使用..." -ForegroundColor Yellow
Get-Process | Where-Object {$_.ProcessName -like "*docker*"} | Format-Table ProcessName, @{Name="Memory(MB)";Expression={[math]::Round($_.WorkingSet/1MB,2)}}

# 4. 检查服务响应
Write-Host "4. 检查服务响应..." -ForegroundColor Yellow
$endpoints = @(
    "http://localhost:3000/api/health",
    "http://localhost:9000/minio/health/live",
    "http://localhost:8000/api/v1/heartbeat"
)

foreach ($endpoint in $endpoints) {
    try {
        $response = Invoke-RestMethod -Uri $endpoint -TimeoutSec 5
        Write-Host "✅ $endpoint" -ForegroundColor Green
    } catch {
        Write-Host "❌ $endpoint" -ForegroundColor Red
    }
}

Write-Host "=== 检查完成 ===" -ForegroundColor Cyan
```

#### 2. 每周维护任务

```powershell
# weekly-maintenance.ps1
Write-Host "=== DL Engine 每周维护 ===" -ForegroundColor Cyan

# 1. 清理Docker系统
Write-Host "1. 清理Docker系统..." -ForegroundColor Yellow
docker system prune -f

# 2. 备份数据
Write-Host "2. 执行数据备份..." -ForegroundColor Yellow
.\backup-windows.ps1

# 3. 更新镜像
Write-Host "3. 检查镜像更新..." -ForegroundColor Yellow
docker-compose -f docker-compose.windows.yml pull

# 4. 重启服务（可选）
$restart = Read-Host "是否重启所有服务？(y/N)"
if ($restart -eq "y" -or $restart -eq "Y") {
    Write-Host "重启服务..." -ForegroundColor Yellow
    .\stop-windows.ps1
    Start-Sleep -Seconds 10
    .\start-windows.ps1
}

Write-Host "=== 维护完成 ===" -ForegroundColor Cyan
```

### 扩展和升级

#### 1. 服务扩展

```powershell
# 扩展协作服务实例
docker-compose -f docker-compose.windows.yml up -d --scale collaboration-service=3

# 扩展渲染服务实例
docker-compose -f docker-compose.windows.yml up -d --scale render-service=2
```

#### 2. 版本升级

```powershell
# upgrade.ps1
Write-Host "=== DL Engine 版本升级 ===" -ForegroundColor Cyan

# 1. 备份当前版本
Write-Host "1. 备份当前版本..." -ForegroundColor Yellow
.\backup-windows.ps1

# 2. 拉取最新代码
Write-Host "2. 更新代码..." -ForegroundColor Yellow
git pull origin main

# 3. 重新构建镜像
Write-Host "3. 重新构建镜像..." -ForegroundColor Yellow
docker-compose -f docker-compose.windows.yml build --no-cache

# 4. 滚动更新服务
Write-Host "4. 滚动更新服务..." -ForegroundColor Yellow
$services = @("api-gateway", "user-service", "project-service", "asset-service", "render-service")

foreach ($service in $services) {
    Write-Host "更新服务: $service" -ForegroundColor Green
    docker-compose -f docker-compose.windows.yml up -d --no-deps $service
    Start-Sleep -Seconds 30
}

Write-Host "=== 升级完成 ===" -ForegroundColor Cyan
```

## 最佳实践

### 1. 开发环境配置

```powershell
# 开发环境快速启动
.\start-windows.ps1 -Profile basic -Build

# 仅启动必要的基础设施服务
# 然后在IDE中启动需要调试的微服务
```

### 2. 生产环境部署

```powershell
# 生产环境完整部署
.\start-windows.ps1 -Profile all -Monitor

# 启用所有服务和监控
```

### 3. 测试环境配置

```powershell
# 测试环境（包含测试数据）
$env:NODE_ENV = "test"
.\start-windows.ps1 -Clean -Build
```

### 4. 性能测试

```powershell
# 性能测试配置
# 1. 增加资源限制
# 2. 启用性能监控
# 3. 配置负载测试工具
```

## 快速参考

### 常用命令

```powershell
# 启动所有服务
.\start-windows.ps1

# 启动基础设施服务
.\start-windows.ps1 -Profile basic

# 重新构建并启动
.\start-windows.ps1 -Clean -Build

# 查看服务状态
docker-compose -f docker-compose.windows.yml ps

# 查看服务日志
docker-compose -f docker-compose.windows.yml logs -f

# 停止所有服务
.\stop-windows.ps1

# 完全清理
.\stop-windows.ps1 -All

# 健康检查
.\health-check-windows.ps1

# 备份数据
.\backup-windows.ps1
```

### 端口映射表

| 服务 | 内部端口 | 外部端口 | 协议 | 说明 |
|------|----------|----------|------|------|
| 前端编辑器 | 80 | 80 | HTTP | Web界面 |
| API网关 | 3000 | 3000 | HTTP | REST API |
| MySQL | 3306 | 3306 | TCP | 数据库 |
| Redis | 6379 | 6379 | TCP | 缓存 |
| MinIO API | 9000 | 9000 | HTTP | 对象存储API |
| MinIO Console | 9001 | 9001 | HTTP | 管理界面 |
| Chroma | 8000 | 8000 | HTTP | 向量数据库 |
| 用户服务 | 3001/4001 | 3001/4001 | gRPC/HTTP | 用户管理 |
| 项目服务 | 3002/4002 | 3002/4002 | gRPC/HTTP | 项目管理 |
| 资产服务 | 3003/4003 | 3003/4003 | gRPC/HTTP | 资产管理 |
| 渲染服务 | 3004/4004 | 3004/4004 | gRPC/HTTP | 渲染处理 |
| 协作服务 | 3005-3006 | 3005-3006 | WebSocket | 实时协作 |
| 协作负载均衡 | 80 | 3007 | HTTP/WS | 负载均衡 |
| AI模型服务 | 8002 | 8002 | HTTP | AI推理 |
| 知识库服务 | 8008 | 8008 | HTTP | 知识管理 |
| RAG引擎 | 8009 | 8009 | HTTP | 检索生成 |

### 环境变量说明

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| MYSQL_ROOT_PASSWORD | DLEngine2024!@# | MySQL root密码 |
| JWT_SECRET | DLEngine2024JWTSecretKey!@#$%^&*() | JWT签名密钥 |
| MINIO_ACCESS_KEY | dlengine | MinIO访问密钥 |
| MINIO_SECRET_KEY | DLEngine2024Storage | MinIO秘密密钥 |
| OPENAI_API_KEY | your_openai_api_key_here | OpenAI API密钥 |
| NODE_ENV | production | 运行环境 |

### 数据目录结构

```
data/
├── mysql/          # MySQL数据文件
├── redis/          # Redis持久化文件
├── minio/          # MinIO对象存储
├── chroma/         # Chroma向量数据库
├── uploads/        # 上传文件
│   ├── assets/     # 资产文件
│   └── knowledge/  # 知识库文件
├── outputs/        # 输出文件
│   └── renders/    # 渲染输出
├── models/         # AI模型文件
├── prometheus/     # Prometheus数据
└── grafana/        # Grafana数据
```

## 总结

本Windows版Docker Compose部署方案为DL Engine数字人交互系统提供了完整的容器化部署解决方案。主要特点包括：

### 核心优势

1. **Windows原生支持**: 专门针对Windows环境优化，充分利用Docker Desktop特性
2. **微服务架构**: 采用现代化微服务设计，支持独立扩展和维护
3. **一键部署**: 提供PowerShell脚本实现一键启动和停止
4. **完整监控**: 集成Prometheus和Grafana监控体系
5. **数据持久化**: 使用Docker卷确保数据安全和持久化
6. **高可用设计**: 支持服务冗余和负载均衡

### 技术栈

- **前端**: React + TypeScript + Ant Design + Three.js
- **后端**: Node.js + NestJS + Python + FastAPI
- **数据库**: MySQL 8.0 + Redis 7.0 + Chroma向量数据库
- **存储**: MinIO对象存储
- **监控**: Prometheus + Grafana
- **容器化**: Docker + Docker Compose

### 适用场景

- **开发环境**: 快速搭建本地开发环境
- **测试环境**: 集成测试和性能测试
- **演示环境**: 产品演示和客户展示
- **小规模生产**: 中小型企业生产部署

### 系统要求

- **操作系统**: Windows 10/11 Pro 或 Windows Server 2019/2022
- **内存**: 最低8GB，推荐16GB+
- **存储**: 最低50GB，推荐100GB+
- **CPU**: 4核心+，推荐8核心+

### 部署流程

1. **环境准备**: 安装Docker Desktop，配置WSL2
2. **代码获取**: 克隆项目代码，配置环境变量
3. **服务启动**: 使用PowerShell脚本一键启动
4. **验证部署**: 检查服务状态，访问Web界面
5. **日常维护**: 定期备份，监控服务状态

### 注意事项

1. **资源配置**: 根据实际需求调整容器资源限制
2. **安全设置**: 修改默认密码，配置防火墙规则
3. **数据备份**: 定期备份重要数据和配置文件
4. **版本管理**: 使用Git管理配置文件版本
5. **监控告警**: 配置监控告警，及时发现问题

### 技术支持

如遇到部署问题，请参考：

1. **故障排除指南**: 查看常见问题解决方案
2. **日志分析**: 使用Docker logs命令查看详细日志
3. **健康检查**: 运行健康检查脚本诊断问题
4. **社区支持**: 查阅Docker和相关技术文档

本部署方案经过充分测试，能够满足大多数Windows环境下的部署需求。通过合理的配置和维护，可以为DL Engine系统提供稳定可靠的运行环境。

---

**文档版本**: v1.0.0
**最后更新**: 2025-01-23
**适用版本**: DL Engine v1.0.0+
**维护团队**: DL Engine开发团队
