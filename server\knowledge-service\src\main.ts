/**
 * 知识库服务入口文件
 */
import { NestFactory } from '@nestjs/core';
import { Transport } from '@nestjs/microservices';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import compression from 'compression';
import helmet from 'helmet';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';

async function bootstrap() {
  // 创建Nest应用实例
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  
  // 配置微服务
  app.connectMicroservice({
    transport: Transport.TCP,
    options: {
      host: configService.get<string>('KNOWLEDGE_SERVICE_HOST', 'localhost'),
      port: configService.get<number>('KNOWLEDGE_SERVICE_MICROSERVICE_PORT', 8018),
    },
  });
  
  // 配置HTTP服务
  // 全局前缀
  app.setGlobalPrefix('api');
  
  // 全局管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );
  
  // 启用CORS
  app.enableCors();
  
  // 启用压缩
  app.use(compression());
  
  // 启用安全头
  app.use(helmet());
  
  // Swagger文档
  const config = new DocumentBuilder()
    .setTitle('知识库服务API')
    .setDescription('DL（Digital Learning）引擎知识库服务API文档')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('knowledge-bases', '知识库管理')
    .addTag('documents', '文档管理')
    .addTag('digital-humans', '数字人管理')
    .addTag('bindings', '知识库绑定')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);
  
  // 启动微服务
  await app.startAllMicroservices();
  
  // 启动HTTP服务
  const httpPort = configService.get<number>('KNOWLEDGE_SERVICE_HTTP_PORT', 8008);
  await app.listen(httpPort);
  console.log(`知识库服务已启动，微服务端口: ${configService.get<number>('KNOWLEDGE_SERVICE_MICROSERVICE_PORT', 8018)}, HTTP端口: ${httpPort}`);
}

bootstrap();
