import { registerAs } from '@nestjs/config';

export interface ProductionKnowledgeConfig {
  // 文件存储配置
  fileStorage: {
    type: 'minio' | 's3' | 'oss';
    endpoint: string;
    accessKey: string;
    secretKey: string;
    bucket: string;
    region?: string;
    cdnDomain?: string;
  };

  // 向量数据库配置
  vectorDatabase: {
    type: 'pinecone' | 'weaviate' | 'milvus' | 'chroma';
    endpoint: string;
    apiKey: string;
    indexName: string;
    dimension: number;
    metric: 'cosine' | 'euclidean' | 'dotproduct';
  };

  // 元数据数据库配置
  metadataDatabase: {
    type: 'postgresql' | 'mysql' | 'mongodb';
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
    ssl: boolean;
  };

  // 缓存配置
  cache: {
    type: 'redis' | 'memcached';
    host: string;
    port: number;
    password?: string;
    ttl: number;
  };

  // 文档处理配置
  documentProcessing: {
    chunkSize: number;
    chunkOverlap: number;
    maxFileSize: number;
    allowedMimeTypes: string[];
    virusScan: boolean;
    quarantinePath: string;
  };

  // 安全配置
  security: {
    jwtSecret: string;
    jwtExpiresIn: string;
    rateLimitMax: number;
    rateLimitWindowMs: number;
    allowedOrigins: string[];
  };
}

export default registerAs('production', (): ProductionKnowledgeConfig => ({
  fileStorage: {
    type: (process.env.FILE_STORAGE_TYPE as any) || 'minio',
    endpoint: process.env.MINIO_ENDPOINT || 'minio:9000',
    accessKey: process.env.MINIO_ACCESS_KEY || 'minioadmin',
    secretKey: process.env.MINIO_SECRET_KEY || 'minioadmin123',
    bucket: process.env.MINIO_BUCKET || 'knowledge-bases',
    region: process.env.MINIO_REGION,
    cdnDomain: process.env.CDN_DOMAIN,
  },

  vectorDatabase: {
    type: (process.env.VECTOR_DB_TYPE as any) || 'chroma',
    endpoint: process.env.VECTOR_DB_ENDPOINT || 'http://chroma:8000',
    apiKey: process.env.VECTOR_DB_API_KEY || '',
    indexName: process.env.VECTOR_DB_INDEX || 'knowledge-base',
    dimension: parseInt(process.env.VECTOR_DB_DIMENSION || '1536'),
    metric: (process.env.VECTOR_DB_METRIC as any) || 'cosine',
  },

  metadataDatabase: {
    type: 'mysql',
    host: process.env.DB_HOST || 'mysql',
    port: parseInt(process.env.DB_PORT || '3306'),
    database: process.env.DB_DATABASE_KNOWLEDGE || process.env.DB_DATABASE || 'dl_engine_knowledge',
    username: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || 'mysql123',
    ssl: process.env.DB_SSL === 'true',
  },

  cache: {
    type: 'redis',
    host: process.env.REDIS_HOST || 'redis',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD || 'redis123',
    ttl: parseInt(process.env.CACHE_TTL || '3600'),
  },

  documentProcessing: {
    chunkSize: parseInt(process.env.CHUNK_SIZE || '1000'),
    chunkOverlap: parseInt(process.env.CHUNK_OVERLAP || '200'),
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '104857600'), // 100MB
    allowedMimeTypes: [
      'text/plain',
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
      'text/html',
      'text/markdown'
    ],
    virusScan: process.env.VIRUS_SCAN === 'true',
    quarantinePath: process.env.QUARANTINE_PATH || '/tmp/quarantine',
  },

  security: {
    jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '24h',
    rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX || '1000'),
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15分钟
    allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  },
}));
