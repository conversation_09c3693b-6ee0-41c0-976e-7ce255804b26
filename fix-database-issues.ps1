# 数据库问题修复脚本
# 解决数据库名称不一致和Chroma服务问题

Write-Host "=== 数据库问题修复脚本 ===" -ForegroundColor Green
Write-Host "正在修复数据库名称不一致和服务问题..." -ForegroundColor Yellow

# 1. 停止所有服务
Write-Host "1. 停止所有服务..." -ForegroundColor Cyan
docker-compose -f docker-compose.windows.yml down

# 2. 清理MySQL数据卷（重要：这会删除现有数据）
Write-Host "2. 清理MySQL数据卷..." -ForegroundColor Cyan
$confirmation = Read-Host "警告：这将删除所有MySQL数据。是否继续？(y/N)"
if ($confirmation -eq 'y' -or $confirmation -eq 'Y') {
    # 删除MySQL数据目录
    if (Test-Path "./data/mysql") {
        Remove-Item -Recurse -Force "./data/mysql"
        Write-Host "MySQL数据目录已清理" -ForegroundColor Green
    }
    
    # 删除Chroma数据目录
    if (Test-Path "./data/chroma") {
        Remove-Item -Recurse -Force "./data/chroma"
        Write-Host "Chroma数据目录已清理" -ForegroundColor Green
    }
} else {
    Write-Host "操作已取消" -ForegroundColor Red
    exit 1
}

# 3. 创建必要的数据目录
Write-Host "3. 创建数据目录..." -ForegroundColor Cyan
$directories = @(
    "./data",
    "./data/mysql",
    "./data/redis", 
    "./data/minio",
    "./data/chroma",
    "./data/elasticsearch",
    "./data/uploads/assets",
    "./data/uploads/knowledge",
    "./data/uploads/asset-library",
    "./data/outputs/renders",
    "./data/models",
    "./data/scene-generation",
    "./data/scene-templates",
    "./data/logs/scene-generation",
    "./data/logs/monitoring",
    "./data/prometheus",
    "./data/grafana"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "创建目录: $dir" -ForegroundColor Gray
    }
}

# 4. 启动基础服务（MySQL, Redis, MinIO）
Write-Host "4. 启动基础服务..." -ForegroundColor Cyan
docker-compose -f docker-compose.windows.yml up -d mysql redis minio

# 5. 等待MySQL启动
Write-Host "5. 等待MySQL启动..." -ForegroundColor Cyan
$maxAttempts = 30
$attempt = 0
do {
    $attempt++
    Write-Host "检查MySQL连接 (尝试 $attempt/$maxAttempts)..." -ForegroundColor Gray
    $result = docker exec dl-engine-mysql-win mysqladmin ping -h localhost -u root -pDLEngine2024!@# 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "MySQL已启动" -ForegroundColor Green
        break
    }
    Start-Sleep -Seconds 5
} while ($attempt -lt $maxAttempts)

if ($attempt -eq $maxAttempts) {
    Write-Host "MySQL启动超时" -ForegroundColor Red
    exit 1
}

# 6. 验证数据库创建
Write-Host "6. 验证数据库创建..." -ForegroundColor Cyan
$databases = docker exec dl-engine-mysql-win mysql -u root -pDLEngine2024!@# -e "SHOW DATABASES;" 2>$null
Write-Host "当前数据库列表:" -ForegroundColor Gray
Write-Host $databases

# 7. 启动Elasticsearch和Chroma
Write-Host "7. 启动Elasticsearch和Chroma..." -ForegroundColor Cyan
docker-compose -f docker-compose.windows.yml up -d elasticsearch chroma

# 8. 等待Elasticsearch和Chroma启动
Write-Host "8. 等待Elasticsearch和Chroma启动..." -ForegroundColor Cyan
Start-Sleep -Seconds 30

# 9. 启动服务注册中心
Write-Host "9. 启动服务注册中心..." -ForegroundColor Cyan
docker-compose -f docker-compose.windows.yml up -d service-registry

# 10. 等待服务注册中心启动
Write-Host "10. 等待服务注册中心启动..." -ForegroundColor Cyan
Start-Sleep -Seconds 20

# 11. 启动其他微服务
Write-Host "11. 启动其他微服务..." -ForegroundColor Cyan
docker-compose -f docker-compose.windows.yml up -d

# 12. 检查服务状态
Write-Host "12. 检查服务状态..." -ForegroundColor Cyan
Start-Sleep -Seconds 10
docker-compose -f docker-compose.windows.yml ps

Write-Host "=== 修复完成 ===" -ForegroundColor Green
Write-Host "请等待几分钟让所有服务完全启动，然后检查服务状态" -ForegroundColor Yellow
Write-Host "使用以下命令检查服务日志:" -ForegroundColor Cyan
Write-Host "docker-compose -f docker-compose.windows.yml logs service-registry" -ForegroundColor Gray
Write-Host "docker logs dl-engine-chroma-win" -ForegroundColor Gray
