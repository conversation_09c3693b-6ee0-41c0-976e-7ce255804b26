version: '3.8'

services:
  # 测试数据库
  mysql-test:
    image: mysql:8.0
    container_name: dl-engine-mysql-test
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: test-password
      MYSQL_ALLOW_EMPTY_PASSWORD: 'no'
      MYSQL_DATABASE: dl_engine_test
    ports:
      - '3307:3306'
    volumes:
      - mysql_test_data:/var/lib/mysql
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - dl-engine-test-network
    healthcheck:
      test: ['CMD', 'mysqladmin', 'ping', '-h', 'localhost', '-u', 'root', '-p${MYSQL_ROOT_PASSWORD}']
      interval: 10s
      timeout: 5s
      retries: 5

  # 测试Redis缓存
  redis-test:
    image: redis:7.0-alpine
    container_name: dl-engine-redis-test
    restart: always
    ports:
      - '6380:6379'
    volumes:
      - redis_test_data:/data
    networks:
      - dl-engine-test-network
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 10s
      timeout: 5s
      retries: 5

  # 测试服务注册中心
  service-registry-test:
    build:
      context: ../../server/service-registry
      dockerfile: Dockerfile.test
    container_name: dl-engine-service-registry-test
    restart: always
    depends_on:
      mysql-test:
        condition: service_healthy
    environment:
      - NODE_ENV=test
      - DB_HOST=mysql-test
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=test-password
      - DB_DATABASE=dl_engine_registry_test
      - SERVICE_REGISTRY_HOST=service-registry-test
      - SERVICE_REGISTRY_PORT=3010
      - SERVICE_REGISTRY_HTTP_PORT=4010
    ports:
      - '3020:3010'
      - '4020:4010'
    networks:
      - dl-engine-test-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4010/health']
      interval: 10s
      timeout: 5s
      retries: 5

  # 测试用户服务
  user-service-test:
    build:
      context: ../../server/user-service
      dockerfile: Dockerfile.test
    container_name: dl-engine-user-service-test
    restart: always
    depends_on:
      mysql-test:
        condition: service_healthy
      service-registry-test:
        condition: service_healthy
    environment:
      - NODE_ENV=test
      - DB_HOST=mysql-test
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=test-password
      - DB_DATABASE=dl_engine_users_test
      - JWT_SECRET=test-jwt-secret
      - JWT_EXPIRES_IN=1d
      - USER_SERVICE_HOST=user-service-test
      - USER_SERVICE_PORT=3001
      - USER_SERVICE_HTTP_PORT=4001
      - SERVICE_REGISTRY_HOST=service-registry-test
      - SERVICE_REGISTRY_PORT=3010
      - REDIS_HOST=redis-test
      - REDIS_PORT=6379
    ports:
      - '3021:3001'
      - '4021:4001'
    networks:
      - dl-engine-test-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4001/health']
      interval: 10s
      timeout: 5s
      retries: 5

  # 测试项目服务
  project-service-test:
    build:
      context: ../../server/project-service
      dockerfile: Dockerfile.test
    container_name: dl-engine-project-service-test
    restart: always
    depends_on:
      mysql-test:
        condition: service_healthy
      service-registry-test:
        condition: service_healthy
    environment:
      - NODE_ENV=test
      - DB_HOST=mysql-test
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=test-password
      - DB_DATABASE=dl_engine_projects_test
      - PROJECT_SERVICE_HOST=project-service-test
      - PROJECT_SERVICE_PORT=3002
      - PROJECT_SERVICE_HTTP_PORT=4002
      - SERVICE_REGISTRY_HOST=service-registry-test
      - SERVICE_REGISTRY_PORT=3010
      - USER_SERVICE_HOST=user-service-test
      - USER_SERVICE_PORT=3001
      - REDIS_HOST=redis-test
      - REDIS_PORT=6379
    ports:
      - '3022:3002'
      - '4022:4002'
    networks:
      - dl-engine-test-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4002/health']
      interval: 10s
      timeout: 5s
      retries: 5

  # 测试资产服务
  asset-service-test:
    build:
      context: ../../server/asset-service
      dockerfile: Dockerfile.test
    container_name: dl-engine-asset-service-test
    restart: always
    depends_on:
      mysql-test:
        condition: service_healthy
      service-registry-test:
        condition: service_healthy
    environment:
      - NODE_ENV=test
      - DB_HOST=mysql-test
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=test-password
      - DB_DATABASE=dl_engine_assets_test
      - ASSET_SERVICE_HOST=asset-service-test
      - ASSET_SERVICE_PORT=3003
      - ASSET_SERVICE_HTTP_PORT=4003
      - SERVICE_REGISTRY_HOST=service-registry-test
      - SERVICE_REGISTRY_PORT=3010
      - USER_SERVICE_HOST=user-service-test
      - USER_SERVICE_PORT=3001
      - PROJECT_SERVICE_HOST=project-service-test
      - PROJECT_SERVICE_PORT=3002
      - JWT_SECRET=test-jwt-secret
      - REDIS_HOST=redis-test
      - REDIS_PORT=6379
      - MAX_FILE_SIZE=10485760
    ports:
      - '3023:3003'
      - '4023:4003'
    volumes:
      - asset_test_uploads:/app/uploads
    networks:
      - dl-engine-test-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4003/health']
      interval: 10s
      timeout: 5s
      retries: 5

networks:
  dl-engine-test-network:
    driver: bridge

volumes:
  mysql_test_data:
  redis_test_data:
  asset_test_uploads:
