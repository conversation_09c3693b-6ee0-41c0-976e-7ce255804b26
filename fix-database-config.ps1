# 数据库配置修复脚本
# 修复MySQL数据库连接问题

Write-Host "开始修复数据库配置问题..." -ForegroundColor Green

# 1. 停止所有容器
Write-Host "停止所有Docker容器..." -ForegroundColor Yellow
docker-compose -f docker-compose.windows.yml down

# 2. 清理MySQL数据卷（可选，会删除所有数据）
$cleanData = Read-Host "是否清理MySQL数据卷？这将删除所有现有数据 (y/N)"
if ($cleanData -eq "y" -or $cleanData -eq "Y") {
    Write-Host "清理MySQL数据卷..." -ForegroundColor Yellow
    docker volume rm newsystem_mysql_data -f
    Remove-Item -Path ".\data\mysql" -Recurse -Force -ErrorAction SilentlyContinue
}

# 3. 创建必要的目录
Write-Host "创建必要的目录..." -ForegroundColor Yellow
$directories = @(
    ".\data\mysql",
    ".\data\redis", 
    ".\data\minio",
    ".\data\chroma",
    ".\data\elasticsearch",
    ".\data\uploads\assets",
    ".\data\uploads\knowledge",
    ".\data\uploads\asset-library",
    ".\data\outputs\renders",
    ".\data\models",
    ".\data\scene-generation",
    ".\data\scene-templates",
    ".\data\logs\scene-generation",
    ".\data\logs\monitoring",
    ".\data\prometheus",
    ".\data\grafana"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "创建目录: $dir" -ForegroundColor Gray
    }
}

# 4. 检查.env文件
Write-Host "检查.env文件配置..." -ForegroundColor Yellow
if (!(Test-Path ".env")) {
    Write-Host "错误: .env文件不存在!" -ForegroundColor Red
    exit 1
}

# 5. 验证MySQL初始化脚本
Write-Host "验证MySQL初始化脚本..." -ForegroundColor Yellow
$initScript = ".\server\shared\init-scripts\mysql\01-create-databases.sql"
if (!(Test-Path $initScript)) {
    Write-Host "错误: MySQL初始化脚本不存在: $initScript" -ForegroundColor Red
    exit 1
}

# 6. 启动基础服务（MySQL, Redis等）
Write-Host "启动基础服务..." -ForegroundColor Yellow
docker-compose -f docker-compose.windows.yml up -d mysql redis minio

# 7. 等待MySQL启动
Write-Host "等待MySQL启动..." -ForegroundColor Yellow
$maxWait = 120
$waited = 0
do {
    Start-Sleep -Seconds 5
    $waited += 5
    $mysqlStatus = docker exec dl-engine-mysql-win mysqladmin ping -h localhost -u root -p$env:MYSQL_ROOT_PASSWORD --silent 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "MySQL已启动" -ForegroundColor Green
        break
    }
    Write-Host "等待MySQL启动... ($waited/$maxWait 秒)" -ForegroundColor Gray
} while ($waited -lt $maxWait)

if ($waited -ge $maxWait) {
    Write-Host "错误: MySQL启动超时" -ForegroundColor Red
    exit 1
}

# 8. 验证数据库创建
Write-Host "验证数据库创建..." -ForegroundColor Yellow
$databases = docker exec dl-engine-mysql-win mysql -u root -p$env:MYSQL_ROOT_PASSWORD -e "SHOW DATABASES;" 2>$null
if ($databases -match "dl_engine_") {
    Write-Host "数据库创建成功" -ForegroundColor Green
    Write-Host $databases
} else {
    Write-Host "警告: 数据库可能未正确创建" -ForegroundColor Yellow
}

# 9. 启动其他基础服务
Write-Host "启动其他基础服务..." -ForegroundColor Yellow
docker-compose -f docker-compose.windows.yml up -d chroma elasticsearch

# 10. 启动服务注册中心
Write-Host "启动服务注册中心..." -ForegroundColor Yellow
Start-Sleep -Seconds 10
docker-compose -f docker-compose.windows.yml up -d service-registry

# 11. 等待服务注册中心启动
Write-Host "等待服务注册中心启动..." -ForegroundColor Yellow
$maxWait = 60
$waited = 0
do {
    Start-Sleep -Seconds 5
    $waited += 5
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:4010/api/health" -TimeoutSec 5 -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) {
            Write-Host "服务注册中心已启动" -ForegroundColor Green
            break
        }
    } catch {
        # 忽略错误，继续等待
    }
    Write-Host "等待服务注册中心启动... ($waited/$maxWait 秒)" -ForegroundColor Gray
} while ($waited -lt $maxWait)

# 12. 启动核心微服务
Write-Host "启动核心微服务..." -ForegroundColor Yellow
$coreServices = @(
    "user-service",
    "project-service", 
    "asset-service"
)

foreach ($service in $coreServices) {
    Write-Host "启动 $service..." -ForegroundColor Gray
    docker-compose -f docker-compose.windows.yml up -d $service
    Start-Sleep -Seconds 10
}

# 13. 启动其他微服务
Write-Host "启动其他微服务..." -ForegroundColor Yellow
docker-compose -f docker-compose.windows.yml up -d

Write-Host "数据库配置修复完成!" -ForegroundColor Green
Write-Host "请检查服务状态: docker-compose -f docker-compose.windows.yml ps" -ForegroundColor Cyan
