import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as Minio from 'minio';
import { LoggerService } from './logger.service';

@Injectable()
export class StorageService implements OnModuleInit {
  private minioClient: Minio.Client;
  private defaultBucket: string;
  private isConnected = false;

  constructor(
    private configService: ConfigService,
    private logger: LoggerService,
  ) {
    this.defaultBucket = this.configService.get('MINIO_BUCKET', 'scene-templates');
  }

  async onModuleInit(): Promise<void> {
    await this.initialize();
  }

  private async initialize(): Promise<void> {
    try {
      // 解析MinIO端点配置
      const minioEndpoint = this.configService.get('MINIO_ENDPOINT', 'localhost:9000');
      let endPoint: string;
      let port: number;

      if (minioEndpoint.includes(':')) {
        const [host, portStr] = minioEndpoint.split(':');
        endPoint = host;
        port = parseInt(portStr) || 9000;
      } else {
        endPoint = minioEndpoint;
        port = parseInt(this.configService.get('MINIO_PORT', '9000'));
      }

      this.minioClient = new Minio.Client({
        endPoint,
        port,
        useSSL: this.configService.get('MINIO_USE_SSL', 'false') === 'true',
        accessKey: this.configService.get('MINIO_ACCESS_KEY', 'minioadmin'),
        secretKey: this.configService.get('MINIO_SECRET_KEY', 'minioadmin'),
      });

      // 设置连接超时检查
      const checkConnection = async () => {
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('MinIO连接超时')), 5000);
        });

        const listBucketsPromise = this.minioClient.listBuckets();

        return Promise.race([listBucketsPromise, timeoutPromise]);
      };

      // 检查连接
      await checkConnection();
      this.isConnected = true;

      // 确保默认存储桶存在
      await this.ensureBucketExists(this.defaultBucket);

      this.logger.log('MinIO存储服务初始化成功', 'StorageService');
    } catch (error) {
      this.logger.error('MinIO存储服务初始化失败', error.message, 'StorageService');
      this.isConnected = false;
      // 不抛出错误，允许服务在没有MinIO的情况下继续运行
    }
  }

  private async ensureBucketExists(bucketName: string): Promise<void> {
    try {
      const exists = await this.minioClient.bucketExists(bucketName);
      if (!exists) {
        await this.minioClient.makeBucket(bucketName);
        this.logger.log(`存储桶创建成功: ${bucketName}`, 'StorageService');
      }
    } catch (error) {
      this.logger.error(`存储桶检查/创建失败: ${bucketName}`, error.message, 'StorageService');
      throw error;
    }
  }

  isStorageConnected(): boolean {
    return this.isConnected;
  }

  async uploadFile(
    file: Buffer,
    fileName: string,
    bucket: string = this.defaultBucket,
  ): Promise<string> {
    if (!this.isConnected) {
      throw new Error('存储服务未连接');
    }

    try {
      await this.ensureBucketExists(bucket);
      await this.minioClient.putObject(bucket, fileName, file);
      return `${bucket}/${fileName}`;
    } catch (error) {
      this.logger.error('文件上传失败', error.message, 'StorageService');
      throw error;
    }
  }

  async downloadFile(bucket: string, fileName: string): Promise<Buffer> {
    if (!this.isConnected) {
      throw new Error('存储服务未连接');
    }

    try {
      const stream = await this.minioClient.getObject(bucket, fileName);
      const chunks: Buffer[] = [];

      return new Promise((resolve, reject) => {
        stream.on('data', (chunk) => chunks.push(chunk));
        stream.on('end', () => resolve(Buffer.concat(chunks)));
        stream.on('error', reject);
      });
    } catch (error) {
      this.logger.error('文件下载失败', error.message, 'StorageService');
      throw error;
    }
  }

  async deleteFile(bucket: string, fileName: string): Promise<void> {
    if (!this.isConnected) {
      throw new Error('存储服务未连接');
    }

    try {
      await this.minioClient.removeObject(bucket, fileName);
      this.logger.log(`文件删除成功: ${fileName}`, 'StorageService');
    } catch (error) {
      this.logger.error('文件删除失败', error.message, 'StorageService');
      throw error;
    }
  }

  async getPresignedUrl(
    bucket: string,
    fileName: string,
    expiry: number = 3600,
  ): Promise<string> {
    if (!this.isConnected) {
      throw new Error('存储服务未连接');
    }

    try {
      return await this.minioClient.presignedGetObject(bucket, fileName, expiry);
    } catch (error) {
      this.logger.error('生成预签名URL失败', error.message, 'StorageService');
      throw error;
    }
  }
}
