-- 创建数据库
CREATE DATABASE IF NOT EXISTS dl_engine_knowledge DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE dl_engine_knowledge;

-- 数字人表
CREATE TABLE IF NOT EXISTS digital_humans (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    model_config J<PERSON><PERSON>,
    voice_config J<PERSON><PERSON>,
    animation_config J<PERSON><PERSON>,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHA<PERSON>(36),
    status VARCHAR(50) DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 知识库表
CREATE TABLE IF NOT EXISTS knowledge_bases (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    language VARCHAR(10) DEFAULT 'zh-CN',
    vector_index_name VARCHAR(255),
    document_count INTEGER DEFAULT 0,
    total_chunks INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(36),
    status VARCHAR(50) DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 知识库文档表
CREATE TABLE IF NOT EXISTS knowledge_documents (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    knowledge_base_id VARCHAR(36),
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255),
    file_path VARCHAR(500),
    file_size BIGINT,
    file_type VARCHAR(100),
    content_hash VARCHAR(64),
    metadata JSON,
    chunk_count INTEGER DEFAULT 0,
    processing_status VARCHAR(50) DEFAULT 'pending',
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    uploaded_by VARCHAR(36),
    FOREIGN KEY (knowledge_base_id) REFERENCES knowledge_bases(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 数字人知识库绑定表
CREATE TABLE IF NOT EXISTS digital_human_knowledge_bindings (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    digital_human_id VARCHAR(36),
    knowledge_base_id VARCHAR(36),
    binding_type VARCHAR(50) DEFAULT 'primary',
    priority INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT true,
    binding_config JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(36),
    UNIQUE(digital_human_id, knowledge_base_id),
    FOREIGN KEY (digital_human_id) REFERENCES digital_humans(id) ON DELETE CASCADE,
    FOREIGN KEY (knowledge_base_id) REFERENCES knowledge_bases(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 文档块表
CREATE TABLE IF NOT EXISTS document_chunks (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    document_id VARCHAR(36),
    chunk_index INTEGER,
    content TEXT NOT NULL,
    content_hash VARCHAR(64),
    start_offset INTEGER,
    end_offset INTEGER,
    metadata JSON,
    vector_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES knowledge_documents(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建索引
CREATE INDEX idx_knowledge_bindings_digital_human ON digital_human_knowledge_bindings(digital_human_id);
CREATE INDEX idx_knowledge_bindings_knowledge_base ON digital_human_knowledge_bindings(knowledge_base_id);
CREATE INDEX idx_documents_knowledge_base ON knowledge_documents(knowledge_base_id);
CREATE INDEX idx_chunks_document ON document_chunks(document_id);
CREATE INDEX idx_documents_status ON knowledge_documents(processing_status);
CREATE INDEX idx_knowledge_bases_category ON knowledge_bases(category);
CREATE INDEX idx_knowledge_bases_language ON knowledge_bases(language);
CREATE INDEX idx_knowledge_bases_status ON knowledge_bases(status);
CREATE INDEX idx_digital_humans_status ON digital_humans(status);
CREATE INDEX idx_documents_uploaded_at ON knowledge_documents(uploaded_at);
CREATE INDEX idx_chunks_created_at ON document_chunks(created_at);
