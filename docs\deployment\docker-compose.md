# DL引擎 Docker Compose 部署指南

本文档提供了使用Docker Compose部署DL（Digital Learning）引擎的详细指南。DL引擎是一个基于微服务架构的3D引擎和编辑器平台，支持大规模并发用户访问。

## 目录

- [系统概述](#系统概述)
- [系统要求](#系统要求)
- [部署架构](#部署架构)
- [前置准备](#前置准备)
- [环境配置](#环境配置)
- [部署步骤](#部署步骤)
- [服务验证](#服务验证)
- [监控和日志](#监控和日志)
- [扩展和优化](#扩展和优化)
- [故障排除](#故障排除)
- [维护和更新](#维护和更新)

## 系统概述

DL引擎采用现代微服务架构，包含以下核心组件：

### 基础设施层
- **MySQL 8.0**：主数据存储，支持多数据库分离
- **Redis 7.0**：缓存和会话存储，支持实时协作
- **MinIO**：对象存储服务，用于资产文件管理

### 核心微服务层
- **服务注册中心**：服务发现和注册（端口：3010/4010）
- **API网关**：统一入口，路由和认证（端口：3000）
- **用户服务**：用户管理和认证（端口：3001/4001）
- **项目服务**：项目和场景管理（端口：3002/4002）
- **资产服务**：资产文件管理（端口：3003/4003）
- **渲染服务**：3D渲染和图像处理（端口：3004/4004）
- **协作服务**：实时协作功能（端口：3005-3007）
- **游戏服务器**：游戏实例管理（端口：3030）

### 前端应用层
- **编辑器**：基于React的可视化编辑器（端口：80）

### 监控和运维层
- **监控服务**：系统监控和告警（端口：3100）
- **Prometheus**：指标收集（端口：9090）
- **Grafana**：监控仪表板（端口：3000）
- **ELK Stack**：日志分析（Elasticsearch:9200, Kibana:5601）

## 系统要求

### 硬件要求
- **CPU**：4核心以上（推荐8核心）
- **内存**：8GB以上（推荐16GB）
- **存储**：50GB以上可用空间（推荐SSD）
- **网络**：稳定的网络连接

### 软件要求
- **操作系统**：Linux、macOS或Windows
- **Docker**：20.10+
- **Docker Compose**：2.0+
- **Git**：用于代码管理

### 端口要求
确保以下端口未被占用：
- **Web服务**：80, 3000
- **微服务**：3001-3007, 3010, 3030, 4001-4010
- **数据库**：3306（MySQL）, 6379（Redis）
- **对象存储**：9000-9001（MinIO）
- **监控**：3100, 5601, 8080, 9090, 9100, 9200

## 部署架构

```
                                    ┌─────────────┐
                                    │   Nginx     │
                                    │  (端口:80)   │
                                    └──────┬──────┘
                                           │
                                    ┌──────┴──────┐
                                    │ API Gateway │
                                    │  (端口:3000) │
                                    └──────┬──────┘
                                           │
                 ┌───────────┬─────────────┼─────────────┬───────────┐
                 │           │             │             │           │
        ┌────────┴────────┐  │  ┌──────────┴─────────┐   │  ┌────────┴────────┐
        │  User Service   │  │  │   Project Service  │   │  │  Asset Service  │
        │   (端口:3001)   │  │  │    (端口:3002)     │   │  │   (端口:3003)   │
        └────────┬────────┘  │  └──────────┬─────────┘   │  └────────┬────────┘
                 │           │             │             │           │
                 │           │             │             │           │
        ┌────────┴────────┐  │  ┌──────────┴─────────┐   │  ┌────────┴────────┐
        │  User Database  │  │  │  Project Database  │   │  │  Asset Database │
        │   (MySQL:3306)  │  │  │    (MySQL:3306)    │   │  │   (MySQL:3306)  │
        └─────────────────┘  │  └────────────────────┘   │  └─────────────────┘
                             │                           │
                     ┌───────┴────────┐          ┌───────┴────────┐
                     │Service Registry│          │ Render Service │
                     │   (端口:3010)   │          │   (端口:3004)   │
                     └────────┬───────┘          └────────┬───────┘
                              │                           │
                     ┌────────┴───────┐          ┌───────┴────────┐
                     │     Redis      │          │     MinIO      │
                     │   (端口:6379)   │          │  (端口:9000)    │
                     └────────────────┘          └────────────────┘
```

## 前置准备

### 1. 安装Docker和Docker Compose

**Ubuntu/Debian:**
```bash
# 更新包索引
sudo apt update

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 将用户添加到docker组
sudo usermod -aG docker $USER
```

**CentOS/RHEL:**
```bash
# 安装Docker
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install -y docker-ce docker-ce-cli containerd.io

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

**Windows:**
下载并安装Docker Desktop for Windows：https://www.docker.com/products/docker-desktop

**macOS:**
下载并安装Docker Desktop for Mac：https://www.docker.com/products/docker-desktop

### 2. 验证安装
```bash
# 验证Docker版本
docker --version

# 验证Docker Compose版本
docker-compose --version

# 测试Docker运行
docker run hello-world
```

### 3. 克隆项目代码
```bash
# 克隆项目仓库
git clone <your-repository-url>
cd newsystem

# 确认项目结构
ls -la
```

## 环境配置

### 1. 创建环境变量文件

创建 `.env` 文件用于配置系统参数：

```bash
# 复制环境变量模板（如果存在）
cp .env.example .env

# 或者创建新的.env文件
touch .env
```

### 2. 配置环境变量

编辑 `.env` 文件，添加以下配置：

```env
# ================================
# 基础配置
# ================================
NODE_ENV=production
COMPOSE_PROJECT_NAME=dl-engine

# ================================
# 数据库配置
# ================================
MYSQL_ROOT_PASSWORD=dl_engine_secure_password_2024
MYSQL_DATABASE=dl_engine
MYSQL_USER=dl_user
MYSQL_PASSWORD=dl_user_password_2024

# ================================
# 安全配置
# ================================
JWT_SECRET=dl_engine_jwt_super_secret_key_2024_very_secure_and_long
JWT_EXPIRES_IN=1d

# ================================
# 网络配置
# ================================
CORS_ORIGIN=*
API_GATEWAY_PORT=3000
EDITOR_PORT=80

# ================================
# 文件上传配置
# ================================
MAX_FILE_SIZE=104857600
UPLOAD_PATH=/app/uploads

# ================================
# MinIO对象存储配置
# ================================
MINIO_ROOT_USER=dl_engine_minio_admin
MINIO_ROOT_PASSWORD=dl_engine_minio_password_2024
MINIO_ACCESS_KEY=dl_engine_access_key
MINIO_SECRET_KEY=dl_engine_secret_key_2024

# ================================
# Redis配置
# ================================
REDIS_PASSWORD=dl_engine_redis_password_2024

# ================================
# 监控配置
# ================================
ELASTICSEARCH_ENABLED=true
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin_password_2024

# ================================
# 邮件配置（用于告警）
# ================================
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your_mail_password
MAIL_FROM=<EMAIL>

# ================================
# 协作服务配置
# ================================
ENABLE_COMPRESSION=true
COMPRESSION_LEVEL=6
MAX_BATCH_SIZE=50
MAX_BATCH_WAIT_TIME=50
```

### 3. 创建必要的目录

```bash
# 创建数据持久化目录
mkdir -p data/mysql
mkdir -p data/redis
mkdir -p data/minio
mkdir -p data/elasticsearch
mkdir -p data/prometheus
mkdir -p data/grafana

# 创建日志目录
mkdir -p logs

# 创建配置目录（如果不存在）
mkdir -p config/{mysql,redis,nginx,prometheus,grafana}

# 设置目录权限
chmod -R 755 data/
chmod -R 755 logs/

## 部署步骤

### 1. 快速部署（推荐）

使用提供的启动脚本进行一键部署：

```bash
# 使用启动脚本
./scripts/start-services.sh

# 脚本会自动执行以下操作：
# - 检查环境变量配置
# - 构建Docker镜像
# - 启动所有服务
# - 等待服务健康检查通过
# - 显示服务状态
```

### 2. 手动部署步骤

如果需要更精细的控制，可以按以下步骤手动部署：

#### 步骤1：构建Docker镜像

```bash
# 构建所有服务镜像
docker-compose build

# 或者单独构建特定服务
docker-compose build api-gateway
docker-compose build user-service
docker-compose build project-service
docker-compose build asset-service
docker-compose build render-service
docker-compose build collaboration-service
docker-compose build editor
```

#### 步骤2：启动基础设施服务

```bash
# 启动数据库和缓存服务
docker-compose up -d mysql redis

# 等待数据库启动完成
echo "等待MySQL启动..."
until docker-compose exec mysql mysqladmin ping -h localhost -u root -p${MYSQL_ROOT_PASSWORD} --silent; do
    echo "MySQL还未就绪，等待5秒..."
    sleep 5
done
echo "MySQL已就绪"

# 等待Redis启动完成
echo "等待Redis启动..."
until docker-compose exec redis redis-cli ping; do
    echo "Redis还未就绪，等待5秒..."
    sleep 5
done
echo "Redis已就绪"
```

#### 步骤3：启动MinIO对象存储

```bash
# 启动MinIO服务
docker-compose up -d minio

# 等待MinIO启动完成
echo "等待MinIO启动..."
until curl -f http://localhost:9000/minio/health/live; do
    echo "MinIO还未就绪，等待5秒..."
    sleep 5
done
echo "MinIO已就绪"

# 初始化MinIO存储桶
docker-compose exec minio mc alias set myminio http://localhost:9000 ${MINIO_ROOT_USER} ${MINIO_ROOT_PASSWORD}
docker-compose exec minio mc mb --ignore-existing myminio/models
docker-compose exec minio mc mb --ignore-existing myminio/textures
docker-compose exec minio mc mb --ignore-existing myminio/audio
docker-compose exec minio mc mb --ignore-existing myminio/renders
docker-compose exec minio mc policy set download myminio/renders
```

#### 步骤4：启动核心微服务

```bash
# 启动服务注册中心
docker-compose up -d service-registry

# 等待服务注册中心启动
echo "等待服务注册中心启动..."
until curl -f http://localhost:4010/health; do
    echo "服务注册中心还未就绪，等待5秒..."
    sleep 5
done
echo "服务注册中心已就绪"

# 启动核心微服务
docker-compose up -d user-service project-service asset-service render-service

# 等待微服务启动完成
echo "等待微服务启动..."
services=("user-service:4001" "project-service:4002" "asset-service:4003" "render-service:4004")
for service in "${services[@]}"; do
    IFS=':' read -r name port <<< "$service"
    until curl -f http://localhost:${port}/health; do
        echo "${name}还未就绪，等待5秒..."
        sleep 5
    done
    echo "${name}已就绪"
done
```

#### 步骤5：启动协作服务

```bash
# 启动协作服务实例
docker-compose up -d collaboration-service-1 collaboration-service-2

# 启动协作服务负载均衡器
docker-compose up -d collaboration-load-balancer

# 等待协作服务启动完成
echo "等待协作服务启动..."
until curl -f http://localhost:3007/health; do
    echo "协作服务还未就绪，等待5秒..."
    sleep 5
done
echo "协作服务已就绪"
```

#### 步骤6：启动API网关

```bash
# 启动API网关
docker-compose up -d api-gateway

# 等待API网关启动完成
echo "等待API网关启动..."
until curl -f http://localhost:3000/api/health; do
    echo "API网关还未就绪，等待5秒..."
    sleep 5
done
echo "API网关已就绪"
```

#### 步骤7：启动前端编辑器

```bash
# 启动编辑器
docker-compose up -d editor

# 等待编辑器启动完成
echo "等待编辑器启动..."
until curl -f http://localhost:80; do
    echo "编辑器还未就绪，等待5秒..."
    sleep 5
done
echo "编辑器已就绪"
```

#### 步骤8：启动游戏服务器（可选）

```bash
# 启动游戏服务器
docker-compose up -d game-server

# 等待游戏服务器启动完成
echo "等待游戏服务器启动..."
until curl -f http://localhost:3030/health; do
    echo "游戏服务器还未就绪，等待5秒..."
    sleep 5
done
echo "游戏服务器已就绪"
```

### 3. 启动监控服务（可选）

```bash
# 启动监控相关服务
docker-compose -f docker-compose.monitoring.yml up -d

# 等待监控服务启动完成
echo "等待监控服务启动..."
services=("prometheus:9090" "grafana:3000" "elasticsearch:9200")
for service in "${services[@]}"; do
    IFS=':' read -r name port <<< "$service"
    until curl -f http://localhost:${port}; do
        echo "${name}还未就绪，等待10秒..."
        sleep 10
    done
    echo "${name}已就绪"
done
```

## 服务验证

### 1. 检查容器状态

```bash
# 查看所有容器状态
docker-compose ps

# 查看特定服务状态
docker-compose ps mysql redis api-gateway

# 查看容器日志
docker-compose logs -f api-gateway
docker-compose logs -f user-service
```

### 2. 健康检查

```bash
# 使用提供的检查脚本
./scripts/check-services.sh

# 手动检查各服务健康状态
echo "检查API网关..."
curl -f http://localhost:3000/api/health

echo "检查服务注册中心..."
curl -f http://localhost:4010/health

echo "检查用户服务..."
curl -f http://localhost:4001/health

echo "检查项目服务..."
curl -f http://localhost:4002/health

echo "检查资产服务..."
curl -f http://localhost:4003/health

echo "检查渲染服务..."
curl -f http://localhost:4004/health

echo "检查协作服务..."
curl -f http://localhost:3007/health

echo "检查编辑器..."
curl -f http://localhost:80
```

### 3. 功能验证

```bash
# 测试用户注册
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "Test@123456",
    "displayName": "测试用户"
  }'

# 测试用户登录
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "Test@123456"
  }'

# 测试MinIO连接
curl -f http://localhost:9000/minio/health/live
```

### 4. 访问地址

部署完成后，可以通过以下地址访问各个服务：

- **编辑器主页**：http://localhost
- **API网关**：http://localhost:3000/api
- **API文档**：http://localhost:3000/api/docs
- **MinIO控制台**：http://localhost:9001
- **Grafana监控**：http://localhost:3000（如果启用监控）
- **Kibana日志**：http://localhost:5601（如果启用监控）
- **Prometheus指标**：http://localhost:9090（如果启用监控）

## 监控和日志

### 1. 启用完整监控栈

```bash
# 启动完整的监控服务
docker-compose -f docker-compose.yml -f docker-compose.monitoring.yml up -d

# 监控服务包括：
# - Prometheus: 指标收集
# - Grafana: 可视化仪表板
# - Elasticsearch: 日志存储
# - Kibana: 日志分析
# - Logstash: 日志处理
# - Filebeat: 日志收集
```

### 2. 配置Grafana仪表板

```bash
# 访问Grafana
open http://localhost:3000

# 默认登录信息
# 用户名: admin
# 密码: admin_password_2024（在.env中配置）

# 导入预配置的仪表板
# 仪表板文件位于: config/grafana/dashboards/
```

### 3. 日志管理

```bash
# 查看实时日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f api-gateway
docker-compose logs -f user-service

# 查看错误日志
docker-compose logs --tail=100 api-gateway | grep ERROR

# 导出日志到文件
docker-compose logs > logs/all-services.log
```

### 4. 性能监控

```bash
# 查看容器资源使用情况
docker stats

# 查看系统资源使用
docker system df

# 查看网络使用情况
docker network ls
docker network inspect dl-engine-network
```

## 扩展和优化

### 1. 水平扩展

```bash
# 扩展特定服务实例数量
docker-compose up -d --scale user-service=3
docker-compose up -d --scale project-service=2
docker-compose up -d --scale asset-service=2

# 扩展协作服务
docker-compose up -d --scale collaboration-service-1=2
docker-compose up -d --scale collaboration-service-2=2
```

### 2. 负载均衡配置

创建 `nginx.conf` 文件用于负载均衡：

```nginx
upstream api_gateway {
    server api-gateway:3000;
}

upstream user_service {
    server user-service-1:4001;
    server user-service-2:4001;
    server user-service-3:4001;
}

server {
    listen 80;
    server_name localhost;

    location /api/ {
        proxy_pass http://api_gateway;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }
}
```

### 3. 性能优化

```bash
# 优化Docker镜像大小
docker image prune -f

# 清理未使用的容器
docker container prune -f

# 清理未使用的网络
docker network prune -f

# 清理未使用的卷
docker volume prune -f

# 系统清理
docker system prune -af
```

### 4. 数据库优化

```sql
-- MySQL性能优化配置
-- 在config/mysql/my.cnf中添加：

[mysqld]
# 基础配置
max_connections = 200
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2

# 查询缓存
query_cache_type = 1
query_cache_size = 64M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

## 故障排除

### 1. 常见问题及解决方案

#### 问题1：容器启动失败

```bash
# 检查容器状态
docker-compose ps

# 查看容器日志
docker-compose logs <service-name>

# 重启特定服务
docker-compose restart <service-name>

# 重新构建并启动
docker-compose up -d --build <service-name>
```

#### 问题2：数据库连接失败

```bash
# 检查MySQL容器状态
docker-compose ps mysql

# 检查MySQL日志
docker-compose logs mysql

# 测试数据库连接
docker-compose exec mysql mysql -u root -p${MYSQL_ROOT_PASSWORD} -e "SHOW DATABASES;"

# 重置数据库密码
docker-compose exec mysql mysql -u root -p${MYSQL_ROOT_PASSWORD} -e "ALTER USER 'root'@'%' IDENTIFIED BY '${MYSQL_ROOT_PASSWORD}';"
```

#### 问题3：服务注册失败

```bash
# 检查服务注册中心状态
curl -f http://localhost:4010/health

# 查看服务注册中心日志
docker-compose logs service-registry

# 检查服务注册情况
curl http://localhost:4010/api/services

# 手动重启服务注册中心
docker-compose restart service-registry
```

#### 问题4：MinIO存储问题

```bash
# 检查MinIO状态
curl -f http://localhost:9000/minio/health/live

# 查看MinIO日志
docker-compose logs minio

# 重新初始化存储桶
docker-compose exec minio mc alias set myminio http://localhost:9000 ${MINIO_ROOT_USER} ${MINIO_ROOT_PASSWORD}
docker-compose exec minio mc mb --ignore-existing myminio/models
```

#### 问题5：端口冲突

```bash
# 检查端口占用
netstat -tulpn | grep :3000
lsof -i :3000

# 修改端口配置
# 编辑docker-compose.yml文件，修改ports映射
# 例如：将"3000:3000"改为"3001:3000"

# 重启服务
docker-compose down
docker-compose up -d
```

### 2. 日志分析

```bash
# 查看错误日志
docker-compose logs | grep -i error

# 查看警告日志
docker-compose logs | grep -i warning

# 分析特定时间段的日志
docker-compose logs --since="2024-01-01T00:00:00" --until="2024-01-01T23:59:59"

# 统计错误数量
docker-compose logs | grep -c "ERROR"
```

### 3. 性能问题诊断

```bash
# 查看容器资源使用
docker stats --no-stream

# 查看系统负载
top
htop

# 查看磁盘使用
df -h
du -sh data/*

# 查看网络连接
netstat -an | grep ESTABLISHED | wc -l
```

### 4. 数据恢复

```bash
# 备份数据库
docker-compose exec mysql mysqldump -u root -p${MYSQL_ROOT_PASSWORD} --all-databases > backup.sql

# 恢复数据库
docker-compose exec -T mysql mysql -u root -p${MYSQL_ROOT_PASSWORD} < backup.sql

# 备份MinIO数据
docker-compose exec minio mc mirror myminio/models /backup/models

# 恢复MinIO数据
docker-compose exec minio mc mirror /backup/models myminio/models

## 维护和更新

### 1. 定期维护任务

#### 每日维护
```bash
# 检查服务状态
./scripts/check-services.sh

# 查看系统资源使用
docker stats --no-stream

# 检查日志错误
docker-compose logs --since="24h" | grep -i error

# 清理临时文件
docker system prune -f
```

#### 每周维护
```bash
# 备份数据库
mkdir -p backups/$(date +%Y%m%d)
docker-compose exec mysql mysqldump -u root -p${MYSQL_ROOT_PASSWORD} --all-databases > backups/$(date +%Y%m%d)/mysql_backup.sql

# 备份MinIO数据
docker-compose exec minio mc mirror myminio backups/$(date +%Y%m%d)/minio

# 清理旧日志
find logs/ -name "*.log" -mtime +7 -delete

# 更新Docker镜像
docker-compose pull
```

#### 每月维护
```bash
# 完整系统备份
tar -czf backups/full_backup_$(date +%Y%m%d).tar.gz data/ config/ logs/

# 清理旧备份（保留3个月）
find backups/ -name "*.tar.gz" -mtime +90 -delete

# 系统性能分析
docker system df
docker system events --since="720h" | grep -i error
```

### 2. 服务更新

#### 更新单个服务
```bash
# 停止特定服务
docker-compose stop user-service

# 重新构建服务
docker-compose build user-service

# 启动更新后的服务
docker-compose up -d user-service

# 验证服务状态
curl -f http://localhost:4001/health
```

#### 滚动更新
```bash
# 创建更新脚本 update-service.sh
#!/bin/bash
SERVICE_NAME=$1

echo "开始更新服务: $SERVICE_NAME"

# 构建新镜像
docker-compose build $SERVICE_NAME

# 创建新容器
docker-compose up -d --no-deps $SERVICE_NAME

# 等待服务就绪
sleep 30

# 验证服务健康状态
if curl -f http://localhost:4001/health; then
    echo "服务更新成功"
else
    echo "服务更新失败，回滚..."
    docker-compose restart $SERVICE_NAME
fi
```

#### 批量更新
```bash
# 更新所有服务
services=("user-service" "project-service" "asset-service" "render-service")

for service in "${services[@]}"; do
    echo "更新服务: $service"
    docker-compose stop $service
    docker-compose build $service
    docker-compose up -d $service
    sleep 30

    # 健康检查
    if ! curl -f http://localhost:4001/health; then
        echo "服务 $service 更新失败"
        exit 1
    fi
done

echo "所有服务更新完成"
```

### 3. 数据库维护

```sql
-- 数据库优化脚本
-- 分析表结构
ANALYZE TABLE users, projects, assets, render_jobs;

-- 优化表
OPTIMIZE TABLE users, projects, assets, render_jobs;

-- 检查表完整性
CHECK TABLE users, projects, assets, render_jobs;

-- 清理过期数据
DELETE FROM render_jobs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
DELETE FROM user_sessions WHERE expires_at < NOW();

-- 更新统计信息
FLUSH TABLES;
```

### 4. 监控告警配置

创建 `config/prometheus/alert-rules.yml`：

```yaml
groups:
- name: dl-engine-alerts
  rules:
  - alert: ServiceDown
    expr: up == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "服务 {{ $labels.instance }} 已停止"
      description: "服务 {{ $labels.instance }} 已停止超过1分钟"

  - alert: HighCPUUsage
    expr: rate(container_cpu_usage_seconds_total[5m]) > 0.8
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "容器 {{ $labels.name }} CPU使用率过高"
      description: "容器 {{ $labels.name }} CPU使用率超过80%"

  - alert: HighMemoryUsage
    expr: container_memory_usage_bytes / container_spec_memory_limit_bytes > 0.9
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "容器 {{ $labels.name }} 内存使用率过高"
      description: "容器 {{ $labels.name }} 内存使用率超过90%"

  - alert: DatabaseConnectionError
    expr: mysql_up == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "数据库连接失败"
      description: "MySQL数据库连接失败超过1分钟"
```

## 安全配置

### 1. 网络安全

```yaml
# 在docker-compose.yml中配置网络隔离
networks:
  frontend:
    driver: bridge
    internal: false
  backend:
    driver: bridge
    internal: true
  database:
    driver: bridge
    internal: true

# 服务网络配置示例
services:
  api-gateway:
    networks:
      - frontend
      - backend

  user-service:
    networks:
      - backend
      - database

  mysql:
    networks:
      - database
```

### 2. 访问控制

```bash
# 配置防火墙规则
sudo ufw enable
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 22/tcp
sudo ufw deny 3306/tcp  # 禁止外部访问数据库
sudo ufw deny 6379/tcp  # 禁止外部访问Redis
```

### 3. SSL/TLS配置

创建 `nginx-ssl.conf`：

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    location / {
        proxy_pass http://api-gateway:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

### 4. 密码和密钥管理

```bash
# 生成强密码
openssl rand -base64 32

# 生成JWT密钥
openssl rand -hex 64

# 使用Docker Secrets（Docker Swarm模式）
echo "your_mysql_password" | docker secret create mysql_password -
echo "your_jwt_secret" | docker secret create jwt_secret -
```

## 生产环境建议

### 1. 硬件配置建议

**小型部署（<100用户）：**
- CPU: 4核心
- 内存: 8GB
- 存储: 100GB SSD
- 网络: 100Mbps

**中型部署（100-1000用户）：**
- CPU: 8核心
- 内存: 16GB
- 存储: 500GB SSD
- 网络: 1Gbps

**大型部署（>1000用户）：**
- CPU: 16核心+
- 内存: 32GB+
- 存储: 1TB+ SSD
- 网络: 10Gbps
- 负载均衡器
- 数据库集群

### 2. 高可用性配置

```yaml
# docker-compose.ha.yml - 高可用配置
version: '3.8'

services:
  mysql-master:
    image: mysql:8.0
    environment:
      MYSQL_REPLICATION_MODE: master
      MYSQL_REPLICATION_USER: replicator
      MYSQL_REPLICATION_PASSWORD: replicator_password

  mysql-slave:
    image: mysql:8.0
    environment:
      MYSQL_REPLICATION_MODE: slave
      MYSQL_MASTER_HOST: mysql-master
      MYSQL_REPLICATION_USER: replicator
      MYSQL_REPLICATION_PASSWORD: replicator_password

  redis-sentinel:
    image: redis:7.0-alpine
    command: redis-sentinel /etc/redis/sentinel.conf
    volumes:
      - ./config/redis/sentinel.conf:/etc/redis/sentinel.conf
```

### 3. 备份策略

```bash
# 自动备份脚本 backup.sh
#!/bin/bash

BACKUP_DIR="/backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR

# 数据库备份
docker-compose exec mysql mysqldump -u root -p${MYSQL_ROOT_PASSWORD} --all-databases > $BACKUP_DIR/mysql.sql

# MinIO备份
docker-compose exec minio mc mirror myminio $BACKUP_DIR/minio

# 配置文件备份
cp -r config/ $BACKUP_DIR/

# 压缩备份
tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR
rm -rf $BACKUP_DIR

# 上传到云存储（可选）
# aws s3 cp $BACKUP_DIR.tar.gz s3://your-backup-bucket/

echo "备份完成: $BACKUP_DIR.tar.gz"
```

### 4. 监控和告警

```bash
# 设置crontab定时任务
crontab -e

# 添加以下任务
# 每5分钟检查服务状态
*/5 * * * * /path/to/scripts/check-services.sh

# 每天凌晨2点备份
0 2 * * * /path/to/scripts/backup.sh

# 每周日凌晨3点清理日志
0 3 * * 0 find /path/to/logs -name "*.log" -mtime +7 -delete
```

## 总结

本文档提供了DL引擎的完整Docker Compose部署指南，包括：

1. **系统架构**：详细的微服务架构说明
2. **环境准备**：Docker安装和配置
3. **部署步骤**：从基础设施到应用服务的完整部署流程
4. **监控运维**：日志管理、性能监控和告警配置
5. **故障排除**：常见问题的诊断和解决方案
6. **维护更新**：定期维护任务和服务更新策略
7. **安全配置**：网络安全、访问控制和数据保护
8. **生产建议**：硬件配置、高可用性和备份策略

通过遵循本指南，您可以成功部署和维护一个稳定、安全、高性能的DL引擎系统。

## 相关文档

- [Kubernetes部署指南](./kubernetes.md)
- [API文档](../api/README.md)
- [开发者指南](../developer/README.md)
- [用户手册](../user-manual/README.md)

## 技术支持

如果在部署过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查项目的GitHub Issues
3. 联系技术支持团队
4. 参考官方文档和社区论坛
```
```
