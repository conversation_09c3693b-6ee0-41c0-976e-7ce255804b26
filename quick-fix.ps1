# 快速修复脚本 - 解决当前数据库和服务问题
Write-Host "=== 快速修复脚本 ===" -ForegroundColor Green

# 1. 停止所有服务
Write-Host "1. 停止所有服务..." -ForegroundColor Cyan
docker-compose -f docker-compose.windows.yml down

# 2. 手动删除错误的数据库并创建正确的数据库
Write-Host "2. 修复数据库..." -ForegroundColor Cyan
docker-compose -f docker-compose.windows.yml up -d mysql

# 等待MySQL启动
Write-Host "等待MySQL启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# 执行数据库修复
Write-Host "执行数据库修复SQL..." -ForegroundColor Cyan
$sqlCommands = @"
-- 删除旧的ir_engine数据库
DROP DATABASE IF EXISTS ir_engine;
DROP DATABASE IF EXISTS ir_engine_registry;
DROP DATABASE IF EXISTS ir_engine_users;
DROP DATABASE IF EXISTS ir_engine_projects;
DROP DATABASE IF EXISTS ir_engine_assets;
DROP DATABASE IF EXISTS ir_engine_render;
DROP DATABASE IF EXISTS ir_engine_knowledge;
DROP DATABASE IF EXISTS ir_engine_ai;
DROP DATABASE IF EXISTS ir_engine_asset_library;
DROP DATABASE IF EXISTS ir_engine_binding;
DROP DATABASE IF EXISTS ir_engine_scene_generation;
DROP DATABASE IF EXISTS ir_engine_scene_templates;
DROP DATABASE IF EXISTS ir_engine_monitoring;

-- 创建正确的dl_engine数据库
CREATE DATABASE IF NOT EXISTS dl_engine_registry CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS dl_engine_users CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS dl_engine_projects CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS dl_engine_assets CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS dl_engine_render CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS dl_engine_knowledge CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS dl_engine_ai CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS dl_engine_asset_library CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS dl_engine_binding CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS dl_engine_scene_generation CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS dl_engine_scene_templates CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS dl_engine_monitoring CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

SHOW DATABASES;
"@

# 将SQL命令写入临时文件
$sqlCommands | Out-File -FilePath "temp_fix.sql" -Encoding UTF8

# 执行SQL命令
Get-Content "temp_fix.sql" | docker exec -i dl-engine-mysql-win mysql -u root -pDLEngine2024!@#

# 删除临时文件
Remove-Item "temp_fix.sql" -Force

# 3. 重新构建服务注册中心镜像
Write-Host "3. 重新构建服务注册中心..." -ForegroundColor Cyan
docker-compose -f docker-compose.windows.yml build service-registry

# 4. 启动基础服务
Write-Host "4. 启动基础服务..." -ForegroundColor Cyan
docker-compose -f docker-compose.windows.yml up -d mysql redis minio elasticsearch

# 5. 等待基础服务启动
Write-Host "5. 等待基础服务启动..." -ForegroundColor Cyan
Start-Sleep -Seconds 45

# 6. 启动Chroma（使用新版本）
Write-Host "6. 启动Chroma..." -ForegroundColor Cyan
docker-compose -f docker-compose.windows.yml up -d chroma

# 7. 等待Chroma启动
Write-Host "7. 等待Chroma启动..." -ForegroundColor Cyan
Start-Sleep -Seconds 30

# 8. 启动服务注册中心
Write-Host "8. 启动服务注册中心..." -ForegroundColor Cyan
docker-compose -f docker-compose.windows.yml up -d service-registry

# 9. 检查服务状态
Write-Host "9. 检查服务状态..." -ForegroundColor Cyan
Start-Sleep -Seconds 20
docker-compose -f docker-compose.windows.yml ps

Write-Host "=== 快速修复完成 ===" -ForegroundColor Green
Write-Host "检查服务注册中心日志:" -ForegroundColor Yellow
docker logs dl-engine-service-registry-win --tail 20
