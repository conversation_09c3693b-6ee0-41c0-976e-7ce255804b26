# 用户服务部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  namespace: dl-engine
  labels:
    app: user-service
    component: microservice
    service: user
    version: v1
spec:
  replicas: 2
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
        component: microservice
        service: user
        version: v1
    spec:
      containers:
      - name: user-service
        image: your-registry/dl-engine-user-service:latest
        ports:
        - containerPort: 3001
          name: tcp
          protocol: TCP
        - containerPort: 4001
          name: http
          protocol: TCP
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: NODE_ENV
        - name: USER_SERVICE_HOST
          value: "0.0.0.0"
        - name: USER_SERVICE_PORT
          value: "3001"
        - name: USER_SERVICE_HTTP_PORT
          value: "4001"
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: DB_PORT
        - name: DB_USERNAME
          value: "root"
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MYSQL_ROOT_PASSWORD
        - name: DB_DATABASE
          value: "dl_engine_users"
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: JWT_SECRET
        - name: JWT_EXPIRES_IN
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: JWT_EXPIRES_IN
        - name: SERVICE_REGISTRY_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_HOST
        - name: SERVICE_REGISTRY_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_PORT
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: REDIS_PORT
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: REDIS_PASSWORD
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 4001
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 4001
          initialDelaySeconds: 15
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - user-service
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: user-service
  namespace: dl-engine
  labels:
    app: user-service
    component: microservice
    service: user
spec:
  type: ClusterIP
  ports:
  - port: 3001
    targetPort: 3001
    protocol: TCP
    name: tcp
  - port: 4001
    targetPort: 4001
    protocol: TCP
    name: http
  selector:
    app: user-service
---
# 用户服务的HPA配置
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: user-service-hpa
  namespace: dl-engine
  labels:
    app: user-service
    component: microservice
    service: user
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: user-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
---
# Pod Disruption Budget
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: user-service-pdb
  namespace: dl-engine
  labels:
    app: user-service
    component: microservice
    service: user
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: user-service
---
# ServiceMonitor - Prometheus监控配置
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: user-service-monitor
  namespace: dl-engine
  labels:
    app: user-service
    component: microservice
    service: user
spec:
  selector:
    matchLabels:
      app: user-service
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s
---
# 网络策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: user-service-netpol
  namespace: dl-engine
  labels:
    app: user-service
    component: microservice
    service: user
spec:
  podSelector:
    matchLabels:
      app: user-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: dl-engine
    ports:
    - protocol: TCP
      port: 3001
    - protocol: TCP
      port: 4001
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: dl-engine
    ports:
    - protocol: TCP
      port: 3306  # MySQL
    - protocol: TCP
      port: 6379  # Redis
    - protocol: TCP
      port: 3010  # Service Registry
  - to: []  # 允许DNS查询
    ports:
    - protocol: UDP
      port: 53
