# DL引擎完整Docker Compose配置文件
# 包含所有核心服务和可选的监控服务
version: '3.8'

services:
  # ================================
  # 基础设施层
  # ================================
  
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: dl-engine-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: dl_engine
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    ports:
      - '3306:3306'
    volumes:
      - mysql_data:/var/lib/mysql
      - ./server/shared/init-scripts:/docker-entrypoint-initdb.d
      - ./config/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
    networks:
      - database-network
    healthcheck:
      test: ['CMD', 'mysqladmin', 'ping', '-h', 'localhost', '-u', 'root', '-p${MYSQL_ROOT_PASSWORD}']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # Redis缓存
  redis:
    image: redis:7.0-alpine
    container_name: dl-engine-redis
    restart: always
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
      - ./config/redis/redis.conf:/etc/redis/redis.conf
    networks:
      - cache-network
    healthcheck:
      test: ['CMD', 'redis-cli', '--no-auth-warning', '-a', '${REDIS_PASSWORD}', 'ping']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # MinIO对象存储
  minio:
    image: minio/minio:RELEASE.2024-01-16T16-07-38Z
    container_name: dl-engine-minio
    restart: always
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    ports:
      - '9000:9000'
      - '9001:9001'
    volumes:
      - minio_data:/data
    networks:
      - storage-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:9000/minio/health/live']
      interval: 30s
      timeout: 20s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # MinIO初始化
  minio-init:
    image: minio/mc:RELEASE.2024-01-13T08-44-48Z
    container_name: dl-engine-minio-init
    depends_on:
      minio:
        condition: service_healthy
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    networks:
      - storage-network
    entrypoint: >
      /bin/sh -c "
      mc alias set myminio http://minio:9000 $$MINIO_ROOT_USER $$MINIO_ROOT_PASSWORD &&
      mc mb --ignore-existing myminio/models &&
      mc mb --ignore-existing myminio/textures &&
      mc mb --ignore-existing myminio/audio &&
      mc mb --ignore-existing myminio/renders &&
      mc policy set download myminio/renders &&
      echo 'MinIO buckets initialized successfully'
      "
    restart: "no"

  # ================================
  # 核心微服务层
  # ================================

  # 服务注册中心
  service-registry:
    build:
      context: ./server/service-registry
      dockerfile: Dockerfile
    container_name: dl-engine-service-registry
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=dl_engine_registry
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - SERVICE_REGISTRY_HTTP_PORT=4010
    ports:
      - '3010:3010'
      - '4010:4010'
    networks:
      - backend-network
      - database-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4010/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # API网关
  api-gateway:
    build:
      context: ./server/api-gateway
      dockerfile: Dockerfile
    container_name: dl-engine-api-gateway
    restart: always
    depends_on:
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3000
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=1d
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - ASSET_SERVICE_HOST=asset-service
      - ASSET_SERVICE_PORT=3003
      - RENDER_SERVICE_HOST=render-service
      - RENDER_SERVICE_PORT=3004
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - CORS_ORIGIN=${CORS_ORIGIN}
    ports:
      - '3000:3000'
    networks:
      - frontend-network
      - backend-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/api/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # 用户服务
  user-service:
    build:
      context: ./server/user-service
      dockerfile: Dockerfile
    container_name: dl-engine-user-service
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=dl_engine_users
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=1d
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - USER_SERVICE_HTTP_PORT=4001
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
    ports:
      - '3001:3001'
      - '4001:4001'
    networks:
      - backend-network
      - database-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4001/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # 项目服务
  project-service:
    build:
      context: ./server/project-service
      dockerfile: Dockerfile
    container_name: dl-engine-project-service
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=dl_engine_projects
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - PROJECT_SERVICE_HTTP_PORT=4002
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
    ports:
      - '3002:3002'
      - '4002:4002'
    networks:
      - backend-network
      - database-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4002/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # 资产服务
  asset-service:
    build:
      context: ./server/asset-service
      dockerfile: Dockerfile
    container_name: dl-engine-asset-service
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
      minio:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=dl_engine_assets
      - ASSET_SERVICE_HOST=asset-service
      - ASSET_SERVICE_PORT=3003
      - ASSET_SERVICE_HTTP_PORT=4003
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - JWT_SECRET=${JWT_SECRET}
      - MAX_FILE_SIZE=${MAX_FILE_SIZE}
      - MINIO_HOST=minio
      - MINIO_PORT=9000
      - MINIO_USE_SSL=false
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_BUCKET_MODELS=models
      - MINIO_BUCKET_TEXTURES=textures
      - MINIO_BUCKET_AUDIO=audio
    ports:
      - '3003:3003'
      - '4003:4003'
    networks:
      - backend-network
      - database-network
      - storage-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4003/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # 渲染服务
  render-service:
    build:
      context: ./server/render-service
      dockerfile: Dockerfile
    container_name: dl-engine-render-service
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=dl_engine_render
      - RENDER_SERVICE_HOST=render-service
      - RENDER_SERVICE_PORT=3004
      - RENDER_SERVICE_HTTP_PORT=4004
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - ASSET_SERVICE_HOST=asset-service
      - ASSET_SERVICE_PORT=3003
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - MINIO_HOST=minio
      - MINIO_PORT=9000
      - MINIO_USE_SSL=false
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_BUCKET_RENDERS=renders
    ports:
      - '3004:3004'
      - '4004:4004'
    networks:
      - backend-network
      - database-network
      - cache-network
      - storage-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4004/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # 协作服务实例1
  collaboration-service-1:
    build:
      context: ./server/collaboration-service
      dockerfile: Dockerfile
    container_name: dl-engine-collaboration-service-1
    restart: always
    depends_on:
      redis:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3005
      - INSTANCE_ID=collaboration-1
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - ENABLE_COMPRESSION=${ENABLE_COMPRESSION}
      - COMPRESSION_LEVEL=${COMPRESSION_LEVEL}
      - MAX_BATCH_SIZE=${MAX_BATCH_SIZE}
      - MAX_BATCH_WAIT_TIME=${MAX_BATCH_WAIT_TIME}
    ports:
      - '3005:3005'
    networks:
      - backend-network
      - cache-network
    healthcheck:
      test: ['CMD', 'wget', '-qO-', 'http://localhost:3005/health']
      interval: 10s
      timeout: 5s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # 协作服务实例2
  collaboration-service-2:
    build:
      context: ./server/collaboration-service
      dockerfile: Dockerfile
    container_name: dl-engine-collaboration-service-2
    restart: always
    depends_on:
      redis:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3006
      - INSTANCE_ID=collaboration-2
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - ENABLE_COMPRESSION=${ENABLE_COMPRESSION}
      - COMPRESSION_LEVEL=${COMPRESSION_LEVEL}
      - MAX_BATCH_SIZE=${MAX_BATCH_SIZE}
      - MAX_BATCH_WAIT_TIME=${MAX_BATCH_WAIT_TIME}
    ports:
      - '3006:3006'
    networks:
      - backend-network
      - cache-network
    healthcheck:
      test: ['CMD', 'wget', '-qO-', 'http://localhost:3006/health']
      interval: 10s
      timeout: 5s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # 协作服务负载均衡
  collaboration-load-balancer:
    image: nginx:alpine
    container_name: dl-engine-collaboration-lb
    restart: always
    depends_on:
      collaboration-service-1:
        condition: service_healthy
      collaboration-service-2:
        condition: service_healthy
    volumes:
      - ./server/collaboration-service/nginx.conf:/etc/nginx/conf.d/default.conf
    ports:
      - '3007:80'
    networks:
      - backend-network
    healthcheck:
      test: ['CMD', 'wget', '-qO-', 'http://localhost:80/health']
      interval: 10s
      timeout: 5s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # 游戏服务器（可选）
  game-server:
    build:
      context: ./server/game-server
      dockerfile: Dockerfile
    container_name: dl-engine-game-server
    restart: always
    depends_on:
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - GAME_SERVER_HOST=game-server
      - GAME_SERVER_PORT=3030
      - GAME_SERVER_MICROSERVICE_PORT=3003
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - JWT_SECRET=${JWT_SECRET}
      - CORS_ORIGIN=${CORS_ORIGIN}
    ports:
      - '3030:3030'
      - '3003:3003'
    networks:
      - backend-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3030/api/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # ================================
  # 前端应用层
  # ================================

  # 编辑器前端
  editor:
    build:
      context: .
      dockerfile: editor/Dockerfile
    container_name: dl-engine-editor
    restart: always
    depends_on:
      api-gateway:
        condition: service_healthy
      collaboration-load-balancer:
        condition: service_healthy
    environment:
      - REACT_APP_API_URL=http://localhost:3000/api
      - REACT_APP_COLLABORATION_SERVER_URL=ws://localhost:3007
      - REACT_APP_ENABLE_DEBUG=false
      - REACT_APP_LOG_LEVEL=error
    ports:
      - '${EDITOR_PORT}:80'
    networks:
      - frontend-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

# ================================
# 网络配置
# ================================
networks:
  frontend-network:
    driver: bridge
    name: dl-engine-frontend
  backend-network:
    driver: bridge
    name: dl-engine-backend
  database-network:
    driver: bridge
    name: dl-engine-database
    internal: true
  cache-network:
    driver: bridge
    name: dl-engine-cache
    internal: true
  storage-network:
    driver: bridge
    name: dl-engine-storage
    internal: true

# ================================
# 数据卷配置
# ================================
volumes:
  mysql_data:
    driver: local
    name: dl-engine-mysql-data
  redis_data:
    driver: local
    name: dl-engine-redis-data
  minio_data:
    driver: local
    name: dl-engine-minio-data
