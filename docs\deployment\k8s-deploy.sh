#!/bin/bash

# DL引擎 Kubernetes 部署脚本
# 作者：DL引擎团队
# 版本：1.0.0
# 描述：自动化部署DL引擎到Kubernetes集群

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
NAMESPACE="dl-engine"
MONITORING_NAMESPACE="dl-engine-monitoring"
REGISTRY="your-registry"  # 请替换为您的容器仓库地址
VERSION="latest"

# 检查必要工具
check_prerequisites() {
    log_info "检查必要工具..."
    
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装，请先安装 kubectl"
        exit 1
    fi
    
    if ! command -v helm &> /dev/null; then
        log_error "helm 未安装，请先安装 helm"
        exit 1
    fi
    
    # 检查集群连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到Kubernetes集群，请检查配置"
        exit 1
    fi
    
    log_success "必要工具检查完成"
}

# 创建命名空间
create_namespaces() {
    log_info "创建命名空间..."
    
    kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    kubectl create namespace $MONITORING_NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    
    log_success "命名空间创建完成"
}

# 生成密码和密钥
generate_secrets() {
    log_info "生成密码和密钥..."
    
    # 生成强密码
    MYSQL_ROOT_PASSWORD=$(openssl rand -base64 32)
    MYSQL_PASSWORD=$(openssl rand -base64 32)
    JWT_SECRET=$(openssl rand -hex 64)
    MINIO_ROOT_USER="dl_engine_admin"
    MINIO_ROOT_PASSWORD=$(openssl rand -base64 32)
    MINIO_ACCESS_KEY=$(openssl rand -base64 20)
    MINIO_SECRET_KEY=$(openssl rand -base64 40)
    REDIS_PASSWORD=$(openssl rand -base64 32)
    GRAFANA_ADMIN_PASSWORD=$(openssl rand -base64 16)
    
    # 保存密码到文件
    cat > secrets.env << EOF
MYSQL_ROOT_PASSWORD=$MYSQL_ROOT_PASSWORD
MYSQL_PASSWORD=$MYSQL_PASSWORD
JWT_SECRET=$JWT_SECRET
MINIO_ROOT_USER=$MINIO_ROOT_USER
MINIO_ROOT_PASSWORD=$MINIO_ROOT_PASSWORD
MINIO_ACCESS_KEY=$MINIO_ACCESS_KEY
MINIO_SECRET_KEY=$MINIO_SECRET_KEY
REDIS_PASSWORD=$REDIS_PASSWORD
GRAFANA_ADMIN_PASSWORD=$GRAFANA_ADMIN_PASSWORD
EOF
    
    log_success "密码和密钥生成完成，已保存到 secrets.env"
}

# 创建ConfigMap
create_configmap() {
    log_info "创建ConfigMap..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: dl-engine-config
  namespace: $NAMESPACE
  labels:
    app: dl-engine
    component: config
data:
  NODE_ENV: "production"
  CORS_ORIGIN: "*"
  SERVICE_REGISTRY_HOST: "service-registry"
  SERVICE_REGISTRY_PORT: "3010"
  SERVICE_REGISTRY_HTTP_PORT: "4010"
  USER_SERVICE_HOST: "user-service"
  USER_SERVICE_PORT: "3001"
  USER_SERVICE_HTTP_PORT: "4001"
  PROJECT_SERVICE_HOST: "project-service"
  PROJECT_SERVICE_PORT: "3002"
  PROJECT_SERVICE_HTTP_PORT: "4002"
  ASSET_SERVICE_HOST: "asset-service"
  ASSET_SERVICE_PORT: "3003"
  ASSET_SERVICE_HTTP_PORT: "4003"
  RENDER_SERVICE_HOST: "render-service"
  RENDER_SERVICE_PORT: "3004"
  RENDER_SERVICE_HTTP_PORT: "4004"
  COLLABORATION_SERVICE_1_PORT: "3005"
  COLLABORATION_SERVICE_2_PORT: "3006"
  COLLABORATION_LOAD_BALANCER_PORT: "3007"
  GAME_SERVER_HOST: "game-server"
  GAME_SERVER_PORT: "3030"
  GAME_SERVER_MICROSERVICE_PORT: "3003"
  DB_HOST: "mysql"
  DB_PORT: "3306"
  DB_USERNAME: "root"
  REDIS_HOST: "redis"
  REDIS_PORT: "6379"
  MINIO_HOST: "minio"
  MINIO_PORT: "9000"
  MINIO_CONSOLE_PORT: "9001"
  MINIO_USE_SSL: "false"
  MINIO_BUCKET_MODELS: "models"
  MINIO_BUCKET_TEXTURES: "textures"
  MINIO_BUCKET_AUDIO: "audio"
  MINIO_BUCKET_RENDERS: "renders"
  MAX_FILE_SIZE: "104857600"
  UPLOAD_PATH: "/app/uploads"
  JWT_EXPIRES_IN: "1d"
  ENABLE_COMPRESSION: "true"
  COMPRESSION_LEVEL: "6"
  MAX_BATCH_SIZE: "50"
  MAX_BATCH_WAIT_TIME: "50"
  API_GATEWAY_PORT: "3000"
  MONITORING_SERVICE_PORT: "3100"
  PROMETHEUS_PORT: "9090"
  GRAFANA_PORT: "3000"
  ELASTICSEARCH_PORT: "9200"
  KIBANA_PORT: "5601"
EOF
    
    log_success "ConfigMap创建完成"
}

# 创建Secret
create_secrets() {
    log_info "创建Secret..."
    
    # 读取密码文件
    source secrets.env
    
    kubectl create secret generic dl-engine-secrets \
        --namespace=$NAMESPACE \
        --from-literal=MYSQL_ROOT_PASSWORD="$MYSQL_ROOT_PASSWORD" \
        --from-literal=MYSQL_PASSWORD="$MYSQL_PASSWORD" \
        --from-literal=JWT_SECRET="$JWT_SECRET" \
        --from-literal=MINIO_ROOT_USER="$MINIO_ROOT_USER" \
        --from-literal=MINIO_ROOT_PASSWORD="$MINIO_ROOT_PASSWORD" \
        --from-literal=MINIO_ACCESS_KEY="$MINIO_ACCESS_KEY" \
        --from-literal=MINIO_SECRET_KEY="$MINIO_SECRET_KEY" \
        --from-literal=REDIS_PASSWORD="$REDIS_PASSWORD" \
        --from-literal=GRAFANA_ADMIN_USER="admin" \
        --from-literal=GRAFANA_ADMIN_PASSWORD="$GRAFANA_ADMIN_PASSWORD" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    log_success "Secret创建完成"
}

# 部署MySQL
deploy_mysql() {
    log_info "部署MySQL数据库..."
    
    # 创建MySQL初始化脚本ConfigMap
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: mysql-init-scripts
  namespace: $NAMESPACE
  labels:
    app: mysql
    component: init-scripts
data:
  01-create-databases.sql: |
    CREATE DATABASE IF NOT EXISTS dl_engine_users CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    CREATE DATABASE IF NOT EXISTS dl_engine_projects CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    CREATE DATABASE IF NOT EXISTS dl_engine_assets CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    CREATE DATABASE IF NOT EXISTS dl_engine_render CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    CREATE DATABASE IF NOT EXISTS dl_engine_registry CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    CREATE DATABASE IF NOT EXISTS dl_engine_monitoring CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    
    CREATE USER IF NOT EXISTS 'dl_user'@'%' IDENTIFIED BY 'dl_user_password';
    GRANT ALL PRIVILEGES ON dl_engine_users.* TO 'dl_user'@'%';
    GRANT ALL PRIVILEGES ON dl_engine_projects.* TO 'dl_user'@'%';
    GRANT ALL PRIVILEGES ON dl_engine_assets.* TO 'dl_user'@'%';
    GRANT ALL PRIVILEGES ON dl_engine_render.* TO 'dl_user'@'%';
    GRANT ALL PRIVILEGES ON dl_engine_registry.* TO 'dl_user'@'%';
    GRANT ALL PRIVILEGES ON dl_engine_monitoring.* TO 'dl_user'@'%';
    FLUSH PRIVILEGES;
EOF
    
    # 使用Helm部署MySQL
    helm repo add bitnami https://charts.bitnami.com/bitnami
    helm repo update
    
    helm upgrade --install mysql bitnami/mysql \
        --namespace $NAMESPACE \
        --set auth.rootPassword="$MYSQL_ROOT_PASSWORD" \
        --set auth.database="dl_engine" \
        --set primary.persistence.size="50Gi" \
        --set primary.resources.requests.memory="1Gi" \
        --set primary.resources.requests.cpu="500m" \
        --set primary.resources.limits.memory="2Gi" \
        --set primary.resources.limits.cpu="1000m" \
        --wait
    
    log_success "MySQL部署完成"
}

# 部署Redis
deploy_redis() {
    log_info "部署Redis缓存..."
    
    helm upgrade --install redis bitnami/redis \
        --namespace $NAMESPACE \
        --set auth.password="$REDIS_PASSWORD" \
        --set master.persistence.size="10Gi" \
        --set master.resources.requests.memory="512Mi" \
        --set master.resources.requests.cpu="250m" \
        --set master.resources.limits.memory="1Gi" \
        --set master.resources.limits.cpu="500m" \
        --set replica.replicaCount=0 \
        --wait
    
    log_success "Redis部署完成"
}

# 部署MinIO
deploy_minio() {
    log_info "部署MinIO对象存储..."
    
    helm upgrade --install minio bitnami/minio \
        --namespace $NAMESPACE \
        --set auth.rootUser="$MINIO_ROOT_USER" \
        --set auth.rootPassword="$MINIO_ROOT_PASSWORD" \
        --set persistence.size="100Gi" \
        --set resources.requests.memory="1Gi" \
        --set resources.requests.cpu="500m" \
        --set resources.limits.memory="2Gi" \
        --set resources.limits.cpu="1000m" \
        --wait
    
    log_success "MinIO部署完成"
}

# 主函数
main() {
    log_info "开始部署DL引擎到Kubernetes集群..."
    
    check_prerequisites
    create_namespaces
    generate_secrets
    create_configmap
    create_secrets
    deploy_mysql
    deploy_redis
    deploy_minio
    
    log_success "DL引擎基础设施部署完成！"
    log_info "接下来请手动部署微服务组件"
    log_info "密码信息已保存到 secrets.env 文件中，请妥善保管"
}

# 执行主函数
main "$@"
