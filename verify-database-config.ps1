# 数据库配置验证脚本
# 验证所有数据库配置修复是否正确

Write-Host "开始验证数据库配置..." -ForegroundColor Green

# 1. 检查.env文件中的数据库配置
Write-Host "`n1. 检查.env文件配置..." -ForegroundColor Yellow
if (Test-Path ".env") {
    $envContent = Get-Content ".env"
    $dbConfigs = $envContent | Where-Object { $_ -match "^DB_DATABASE" }
    
    Write-Host "数据库配置:" -ForegroundColor Cyan
    foreach ($config in $dbConfigs) {
        if ($config -match "dl_engine_") {
            Write-Host "  ✓ $config" -ForegroundColor Green
        } elseif ($config -match "ir_engine_") {
            Write-Host "  ✗ $config (需要修复)" -ForegroundColor Red
        } else {
            Write-Host "  ? $config" -ForegroundColor Yellow
        }
    }
} else {
    Write-Host "  ✗ .env文件不存在" -ForegroundColor Red
}

# 2. 检查docker-compose.windows.yml配置
Write-Host "`n2. 检查docker-compose.windows.yml配置..." -ForegroundColor Yellow
if (Test-Path "docker-compose.windows.yml") {
    $composeContent = Get-Content "docker-compose.windows.yml"
    $mysqlDbLine = $composeContent | Where-Object { $_ -match "MYSQL_DATABASE:" }
    
    if ($mysqlDbLine -match "dl_engine_registry") {
        Write-Host "  ✓ MySQL初始数据库配置正确" -ForegroundColor Green
    } else {
        Write-Host "  ✗ MySQL初始数据库配置错误: $mysqlDbLine" -ForegroundColor Red
    }
    
    # 检查初始化脚本挂载
    $initScriptMount = $composeContent | Where-Object { $_ -match "init-scripts" }
    if ($initScriptMount) {
        Write-Host "  ✓ MySQL初始化脚本已挂载" -ForegroundColor Green
    } else {
        Write-Host "  ✗ MySQL初始化脚本未挂载" -ForegroundColor Red
    }
} else {
    Write-Host "  ✗ docker-compose.windows.yml文件不存在" -ForegroundColor Red
}

# 3. 检查MySQL初始化脚本
Write-Host "`n3. 检查MySQL初始化脚本..." -ForegroundColor Yellow
$initScript = "server\shared\init-scripts\mysql\01-create-databases.sql"
if (Test-Path $initScript) {
    $scriptContent = Get-Content $initScript
    $expectedDatabases = @(
        "dl_engine_registry",
        "dl_engine_users", 
        "dl_engine_projects",
        "dl_engine_assets",
        "dl_engine_render",
        "dl_engine_knowledge",
        "dl_engine_ai",
        "dl_engine_asset_library",
        "dl_engine_binding",
        "dl_engine_scene_generation",
        "dl_engine_scene_templates",
        "dl_engine_monitoring"
    )
    
    foreach ($db in $expectedDatabases) {
        if ($scriptContent -match $db) {
            Write-Host "  ✓ $db" -ForegroundColor Green
        } else {
            Write-Host "  ✗ $db (缺失)" -ForegroundColor Red
        }
    }
} else {
    Write-Host "  ✗ MySQL初始化脚本不存在: $initScript" -ForegroundColor Red
}

# 4. 检查微服务数据库配置
Write-Host "`n4. 检查微服务数据库配置..." -ForegroundColor Yellow

$services = @(
    @{Name="项目服务"; Path="server\project-service\src\app.module.ts"; Pattern="DB_DATABASE_PROJECTS"},
    @{Name="用户服务"; Path="server\user-service\src\app.module.ts"; Pattern="DB_DATABASE_USERS"},
    @{Name="服务注册中心"; Path="server\service-registry\src\app.module.ts"; Pattern="DB_DATABASE_REGISTRY"},
    @{Name="资产服务"; Path="server\asset-service\src\app.module.ts"; Pattern="DB_DATABASE_ASSETS"},
    @{Name="渲染服务"; Path="server\render-service\src\app.module.ts"; Pattern="DB_DATABASE_RENDER"},
    @{Name="监控服务"; Path="server\monitoring-service\src\app.module.ts"; Pattern="DB_DATABASE_MONITORING"}
)

foreach ($service in $services) {
    if (Test-Path $service.Path) {
        $content = Get-Content $service.Path
        if ($content -match $service.Pattern) {
            Write-Host "  ✓ $($service.Name)" -ForegroundColor Green
        } else {
            Write-Host "  ? $($service.Name) (可能使用默认配置)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "  ✗ $($service.Name) (文件不存在)" -ForegroundColor Red
    }
}

# 5. 检查Docker容器状态（如果正在运行）
Write-Host "`n5. 检查Docker容器状态..." -ForegroundColor Yellow
try {
    $containers = docker ps --format "table {{.Names}}\t{{.Status}}" 2>$null
    if ($containers) {
        Write-Host "当前运行的容器:" -ForegroundColor Cyan
        Write-Host $containers
        
        # 检查MySQL容器
        $mysqlContainer = docker ps --filter "name=dl-engine-mysql" --format "{{.Names}}" 2>$null
        if ($mysqlContainer) {
            Write-Host "`n检查MySQL数据库..." -ForegroundColor Cyan
            try {
                $databases = docker exec $mysqlContainer mysql -u root -p$env:MYSQL_ROOT_PASSWORD -e "SHOW DATABASES;" 2>$null
                if ($databases -match "dl_engine_") {
                    Write-Host "  ✓ MySQL数据库已创建" -ForegroundColor Green
                    $databases | Where-Object { $_ -match "dl_engine_" } | ForEach-Object {
                        Write-Host "    - $_" -ForegroundColor Gray
                    }
                } else {
                    Write-Host "  ✗ MySQL数据库未创建或连接失败" -ForegroundColor Red
                }
            } catch {
                Write-Host "  ? 无法连接到MySQL数据库" -ForegroundColor Yellow
            }
        } else {
            Write-Host "  - MySQL容器未运行" -ForegroundColor Gray
        }
    } else {
        Write-Host "  - 没有运行的Docker容器" -ForegroundColor Gray
    }
} catch {
    Write-Host "  ? Docker未运行或无法访问" -ForegroundColor Yellow
}

# 6. 生成修复建议
Write-Host "`n6. 修复建议..." -ForegroundColor Yellow
Write-Host "如果发现问题，请按以下步骤修复:" -ForegroundColor Cyan
Write-Host "1. 运行修复脚本: .\fix-database-config.ps1" -ForegroundColor White
Write-Host "2. 或手动重启服务: docker-compose -f docker-compose.windows.yml down && docker-compose -f docker-compose.windows.yml up -d" -ForegroundColor White
Write-Host "3. 检查服务日志: docker-compose -f docker-compose.windows.yml logs [service-name]" -ForegroundColor White

Write-Host "`n验证完成!" -ForegroundColor Green
